# Use official Python image
FROM python:3.9-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    python3-dev \
    python3-distutils \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Add app directory to Python path
ENV PYTHONPATH="${PYTHONPATH}:/app"

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create __init__.py files
RUN mkdir -p /app/web && \
    touch /app/__init__.py && \
    touch /app/web/__init__.py

# Create directories for models and plots
RUN mkdir -p models plots

# Expose port
EXPOSE 5000

# Run application
CMD ["python", "web/app.py"]
