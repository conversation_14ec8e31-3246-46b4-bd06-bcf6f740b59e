/# Snake Reinforcement Learning Project

A complete snake game implementation with reinforcement learning training and web interface.

## Key Features

- **Enhanced Training System**:
  - Fixed time-related errors
  - Proper error handling and logging
  - UI state management
  - Auto-refresh (2s interval)
  - Correct progress calculation

- **Web Interface**:
  - Real-time training visualization
  - Interactive controls
  - Training statistics dashboard
  - Model evaluation display

- **Evaluation Section**:
  - Displays mean/max scores
  - Shows action distribution
  - Proper error context
  - Smooth scrolling to results

- **Replay Functionality**:
  - Handles array data
  - Shows final model performance
  - Fallback API support
  - Error recovery mechanisms

- **Robust Connectivity**:
  - WebSocket monitoring
  - Status polling
  - Connection indicators
  - Automatic reconnection

## Recent Fixes & Improvements

1. Resolved 'time is not defined' errors
2. Added explicit time imports in all relevant files
3. Enhanced error handling during training completion
4. Added debug logging for imports and operations
5. Improved container rebuild process
6. Fixed UI update issues
7. Added proper training completion handling

## Installation

```bash
# Ensure Docker is installed
docker --version

# Build and run
cd snake-rl
docker compose build --no-cache
docker compose up
```

## Usage

1. Access web interface at `http://localhost`
2. Start training via the web interface
3. Monitor:
   - Training progress with correct percentages
   - Score updates in real-time
   - Evaluation metrics
   - Final model replay

## Project Structure

```
snake-rl/
├── web/                   # Web interface implementation
│   ├── app.py             # Flask backend
│   └── static/            # Frontend assets
├── snake_game.py          # Core game implementation
├── agent.py               # RL agent implementation
├── training.py            # Training loop
├── evaluation.py          # Testing and metrics
├── docker-compose.yml     # Container configuration
├── requirements.txt       # Python dependencies
└── README.md              # Documentation
```

## Troubleshooting

- If seeing 'time is not defined' errors:
  - Ensure containers are rebuilt with `--no-cache`
  - Verify all time imports exist in:
    - web/app.py
    - training.py  
    - evaluation.py

- For UI issues:
  - Check browser console for errors
  - Verify WebSocket connection status
  - Try force-refreshing the page (Ctrl+F5)

- For training problems:
  - Check `snake_rl.log` for detailed errors
  - Ensure sufficient system resources
  - Verify Docker container logs

## Customization

You can modify various parameters in the configuration:

```python
# In config.py
EPISODES = 100000         # Total training episodes
BATCH_SIZE = 32           # Training batch size
LEARNING_RATE = 0.0005    # Agent learning rate
MEMORY_SIZE = 5000        # Experience replay buffer size
```
