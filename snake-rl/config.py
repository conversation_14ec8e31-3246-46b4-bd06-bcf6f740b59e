# Reinforcement Learning Configuration
class Config:
    # Training Parameters
    EPISODES = 100
    BATCH_SIZE = 32
    SAVE_INTERVAL = 100
    RENDER_INTERVAL = 500
    DEFAULT_MEMORY_RETENTION = 0.7  # Keep 70% newest experiences when loading
    
    # Agent Hyperparameters
    GAMMA = 0.99  # Discount factor
    EPSILON_START = 1.0
    EPSILON_MIN = 0.01
    EPSILON_DECAY = 0.999
    LEARNING_RATE = 0.0005
    MEMORY_SIZE = 5000
    
    # Game Rewards
    REWARD_FOOD = 10
    REWARD_STEP = -0.1
    REWARD_CLOSER = 1
    REWARD_FARTHER = -1
    REWARD_WIN = 50
    REWARD_DEATH = -10
    
    # Environment Settings
    GRID_SIZE = 10
    MAX_STEPS = 100