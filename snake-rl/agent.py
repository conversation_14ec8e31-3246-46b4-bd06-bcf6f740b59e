import numpy as np
from typing import Dict, Any
from collections import deque
import random

class RLAgent:
    """Reinforcement Learning Agent for Snake Game"""
    
    def __init__(self, state_size: tuple, action_size: int):
        from config import Config
        self.state_size = (Config.GRID_SIZE, Config.GRID_SIZE)
        self.action_size = action_size
        self.direction = (0, 1)  # Initial direction (right)
        
        # Hyperparameters from config
        self.gamma = Config.GAMMA
        self.epsilon = Config.EPSILON_START
        self.epsilon_min = Config.EPSILON_MIN
        self.epsilon_decay = Config.EPSILON_DECAY
        self.learning_rate = Config.LEARNING_RATE
        
        # Experience replay memory
        self.memory = deque(maxlen=Config.MEMORY_SIZE)
        # Track last actions to balance exploration
        self.last_actions = deque(maxlen=10)
        
        # Optimized state representation with key features:
        # head_x: 0-9, head_y: 0-9 (10x10 grid)
        # food_dir: 0-3 (quadrant relative to head)
        # danger: 0-2 (none, near, collision)
        self.q_table = np.zeros((10, 10, 4, 3, 3), dtype=np.float16)  # Much smaller table
    
    def remember(self, state: np.ndarray, action: int, reward: float, next_state: np.ndarray, done: bool):
        """Store experience in memory"""
        self.memory.append((state, action, reward, next_state, done))
    
    def act(self, state: np.ndarray) -> int:
        """Select action using epsilon-greedy policy with balanced exploration"""
        if np.random.rand() <= self.epsilon:
            # Balance exploration to reduce bias
            action = random.randrange(self.action_size)
            if len(self.last_actions) > 5:
                # Reduce probability of repeating dominant action
                if action == max(set(self.last_actions), key=self.last_actions.count):
                    action = random.choice([a for a in range(3) if a != action])
            self.last_actions.append(action)
            return action
        
        # Exploitation
        state_features = self._get_simplified_state(state)
        action = np.argmax(self.q_table[tuple(state_features)])
        self.last_actions.append(action)
        return action
    
    def replay(self, batch_size: int) -> float:
        """Train on batch from memory"""
        if len(self.memory) < batch_size:
            return 0.0
        
        minibatch = random.sample(self.memory, batch_size)
        total_loss = 0.0
        
        for state, action, reward, next_state, done in minibatch:
            # Get simplified state representations
            state_features = self._get_simplified_state(state)
            next_features = self._get_simplified_state(next_state)
            
            target = reward
            if not done:
                target = reward + self.gamma * np.amax(
                    self.q_table[tuple(next_features)]
                )
            
            current_q = self.q_table[tuple(state_features) + (action,)]
            self.q_table[tuple(state_features) + (action,)] += \
                self.learning_rate * (target - current_q)
            
            total_loss += abs(target - current_q)
        
        # Decay epsilon
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
            
        return total_loss / batch_size
    
    def _get_simplified_state(self, state: np.ndarray) -> tuple:
        """Optimized state representation with key features"""
        # Find positions
        head_pos = np.unravel_index(np.argmax(state == 1.0), state.shape)
        food_pos = np.unravel_index(np.argmax(state == -1.0), state.shape)
        
        # Convert coordinates with bounds checking
        head_x, head_y = min(max(head_pos[0], 0), 9), min(max(head_pos[1], 0), 9)
        food_x, food_y = min(max(food_pos[0], 0), 9), min(max(food_pos[1], 0), 9)
        
        # Food direction quadrant (0-3)
        if food_x < head_x and food_y <= head_y:
            food_dir = 0  # Top-left
        elif food_x >= head_x and food_y < head_y:
            food_dir = 1  # Top-right
        elif food_x < head_x and food_y > head_y:
            food_dir = 2  # Bottom-left
        else:
            food_dir = 3  # Bottom-right
        
        # Danger detection (0=none, 1=near, 2=collision)
        danger = 0
        if self._check_immediate_danger(state, head_pos):
            danger = 2
        elif self._check_near_danger(state, head_pos):
            danger = 1
            
        return (head_x, head_y, food_dir, danger)

    def _check_immediate_danger(self, state: np.ndarray, head_pos: tuple) -> bool:
        """Check for immediate collision danger"""
        x, y = head_pos
        # Check walls
        if (self.direction == (0, 1) and y == 9) or \
           (self.direction == (0, -1) and y == 0) or \
           (self.direction == (1, 0) and x == 9) or \
           (self.direction == (-1, 0) and x == 0):
            return True
        # Check body
        next_pos = (x + self.direction[0], y + self.direction[1])
        return state[next_pos] == 0.5

    def _check_near_danger(self, state: np.ndarray, head_pos: tuple) -> bool:
        """Check for nearby danger (1 step away)"""
        x, y = head_pos
        # Check if near walls
        if (self.direction == (0, 1) and y >= 8) or \
           (self.direction == (0, -1) and y <= 1) or \
           (self.direction == (1, 0) and x >= 8) or \
           (self.direction == (-1, 0) and x <= 1):
            return True
        # Check if body is nearby
        next_pos = (x + self.direction[0], y + self.direction[1])
        return state[next_pos] == 0.5
    
    def _check_body_ahead(self, state: np.ndarray, head_pos: tuple) -> int:
        """Check if body is in front of head"""
        x, y = head_pos
        if self.direction == (0, 1):  # Right
            return 1 if y < 9 and state[x, y+1] == 0.5 else 0
        elif self.direction == (0, -1):  # Left
            return 1 if y > 0 and state[x, y-1] == 0.5 else 0
        elif self.direction == (1, 0):  # Down
            return 1 if x < 9 and state[x+1, y] == 0.5 else 0
        else:  # Up
            return 1 if x > 0 and state[x-1, y] == 0.5 else 0

    def _check_body_left(self, state: np.ndarray, head_pos: tuple) -> int:
        """Check if body is to the left of head"""
        x, y = head_pos
        if self.direction == (0, 1):  # Right
            return 1 if x > 0 and state[x-1, y] == 0.5 else 0
        elif self.direction == (0, -1):  # Left
            return 1 if x < 9 and state[x+1, y] == 0.5 else 0
        elif self.direction == (1, 0):  # Down
            return 1 if y > 0 and state[x, y-1] == 0.5 else 0
        else:  # Up
            return 1 if y < 9 and state[x, y+1] == 0.5 else 0

    def _check_body_right(self, state: np.ndarray, head_pos: tuple) -> int:
        """Check if body is to the right of head"""
        x, y = head_pos
        if self.direction == (0, 1):  # Right
            return 1 if x < 9 and state[x+1, y] == 0.5 else 0
        elif self.direction == (0, -1):  # Left
            return 1 if x > 0 and state[x-1, y] == 0.5 else 0
        elif self.direction == (1, 0):  # Down
            return 1 if y < 9 and state[x, y+1] == 0.5 else 0
        else:  # Up
            return 1 if y > 0 and state[x, y-1] == 0.5 else 0

    def save(self, path: str):
        """Save agent weights and memory to file"""
        save_data = {
            'q_table': self.q_table,
            'memory': list(self.memory),
            'last_actions': list(self.last_actions)
        }
        np.save(path, save_data)
    
    def load(self, path: str):
        """Load agent weights and memory from file"""
        data = np.load(path, allow_pickle=True).item()
        self.q_table = data['q_table']
        self.memory = deque(data['memory'], maxlen=5000)
        self.last_actions = deque(data['last_actions'], maxlen=10)
