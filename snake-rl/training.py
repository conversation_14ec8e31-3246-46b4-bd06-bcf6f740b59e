from collections import deque
import numpy as np
from snake_game import <PERSON><PERSON><PERSON>
from agent import RLA<PERSON>
from typing import Dict, List
import time
import os

class Trainer:
    """Handles training of RL agent on Snake Game"""
    
    def __init__(self, load_model=False, extended_training=False, keep_memory=1.0, use_color=False):
        from config import Config
        
        # Initialize environment
        self.env = SnakeGame()
        
        # Initialize agent
        self.agent = RLAgent(state_size=(Config.GRID_SIZE, Config.GRID_SIZE), action_size=3)
        
        # Load existing model if requested
        if load_model and os.path.exists("models/snake_agent_final.npy"):
            self.agent.load("models/snake_agent_final.npy")
            print(f"Loaded existing model (Memory kept: {keep_memory*100:.0f}%)")
            
            # Control memory retention
            if keep_memory < 1.0:
                current_size = len(self.agent.memory)
                new_size = int(current_size * keep_memory)
                self.agent.memory = deque(list(self.agent.memory)[-new_size:], maxlen=Config.MEMORY_SIZE)
        
        # Training parameters from config with validation
        self.episodes = max(1, Config.EPISODES)
        self.batch_size = max(1, Config.BATCH_SIZE)
        self.save_interval = max(1, Config.SAVE_INTERVAL)
        self.render_interval = max(1, Config.RENDER_INTERVAL)
        
        # Store color preference
        self.use_color = use_color
        
        # Extended training adjustments
        if extended_training:
            self.episodes = max(1, Config.EPISODES * 5)  # 5x longer training
            self.agent.learning_rate = max(0.0001, Config.LEARNING_RATE / 2)  # Slower learning
        
        # Logging
        self.scores: List[int] = []
        self.epsilons: List[float] = []
        self.losses: List[float] = []
        
        # Create models directory
        os.makedirs('models', exist_ok=True)
    
    def train(self):
        """Main training loop"""
        print("Starting training...")
        start_time = time.time()
        
        for e in range(self.episodes):
            state = self.env.reset()
            total_reward = 0
            done = False
            
            while not done:
                # Get action from agent
                action = self.agent.act(state)
                
                # Take step in environment
                next_state, reward, done, _ = self.env.step(action)
                
                # Store experience and verify
                self.agent.remember(state, action, reward, next_state, done)
                if e % 100 == 0:  # Log memory status periodically
                    print(f"Memory size: {len(self.agent.memory)} | Last reward: {reward}")
                
                # Train on batch
                loss = self.agent.replay(self.batch_size)
                if loss > 0:
                    self.losses.append(loss)
                
                total_reward += reward
                state = next_state
                
                # Optional rendering (skip if interval is 0)
                if self.render_interval > 0 and e % self.render_interval == 0:
                    self.env.render(use_color=self.use_color)
                    time.sleep(0.1)
            
            # Log episode results
            self.scores.append(self.env.score)
            self.epsilons.append(self.agent.epsilon)
            
            # Print progress
            if e % 10 == 0:
                avg_score = np.mean(self.scores[-10:])
                print(f"Episode {e}/{self.episodes} | "
                      f"Score: {self.env.score} | "
                      f"Avg Score: {avg_score:.1f} | "
                      f"Epsilon: {self.agent.epsilon:.2f}")
            
            # Save model periodically with cleanup
            if e % self.save_interval == 0 and e > 0:
                # Remove previous checkpoint
                prev_checkpoint = f"models/snake_agent_{e-self.save_interval}.npy"
                if os.path.exists(prev_checkpoint):
                    os.remove(prev_checkpoint)
                # Save new checkpoint
                self.agent.save(f"models/snake_agent_{e}.npy")
        
        # Save final model and clean up
        for f in os.listdir("models"):
            if f.startswith("snake_agent_") and not f.endswith("final.npy"):
                os.remove(f"models/{f}")
        self.agent.save("models/snake_agent_final.npy")
        print(f"Training completed in {time.time() - start_time:.1f} seconds")
        
        return {
            'scores': self.scores,
            'epsilons': self.epsilons,
            'losses': self.losses
        }

if __name__ == "__main__":
    import argparse
    from config import Config
    
    parser = argparse.ArgumentParser(description='Train Snake RL Agent')
    parser.add_argument('--load', action='store_true', help='Load existing model')
    parser.add_argument('--extended', action='store_true', help='Use extended training')
    parser.add_argument('--keep_memory', type=float, default=Config.DEFAULT_MEMORY_RETENTION,
                      help='Fraction of memory to keep when loading (0.0-1.0)')
    parser.add_argument('--color', action='store_true',
                      help='Enable colored output')
    args = parser.parse_args()
    
    trainer = Trainer(
        load_model=args.load,
        extended_training=args.extended,
        keep_memory=args.keep_memory,
        use_color=args.color
    )
    trainer.train()
