// Game rendering constants
const GRID_SIZE = 10;
const CELL_SIZE = 40;
const COLORS = {
    background: '#222',
    snakeHead: '#2ecc71',
    snakeBody: '#27ae60',
    food: '#e74c3c',
    text: '#ecf0f1'
};

// Game state
let currentEpisode = null;
let gameStates = [];
let currentFrame = 0;
let isPlaying = false;
let playInterval = null;
let playSpeed = 5;

// DOM elements
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const frameCounter = document.getElementById('frameCounter');
const prevFrameBtn = document.getElementById('prevFrame');
const nextFrameBtn = document.getElementById('nextFrame');
const playPauseBtn = document.getElementById('playPause');
const speedControl = document.getElementById('speedControl');
const startTrainingBtn = document.getElementById('startTraining');
const stopTrainingBtn = document.getElementById('stopTraining');
const evaluateBtn = document.getElementById('evaluateModel');
const episodeEl = document.getElementById('episode');
const scoreEl = document.getElementById('score');
const avgScoreEl = document.getElementById('avgScore');
const epsilonEl = document.getElementById('epsilon');
const progressBar = document.getElementById('trainingProgress');
const progressText = document.getElementById('progressText');

// API configuration - direct Flask routes
const API_BASE = '/api';

// No WebSocket - use simple REST API polling
document.getElementById('connectionStatus').textContent = 'REST API';
document.getElementById('connectionStatus').style.color = 'blue';

// Auto-refresh interval - poll every 2 seconds
let refreshInterval = setInterval(() => {
    if (document.hidden) return;
    fetchTrainingStatus();
}, 2000);

async function fetchTrainingStatus() {
    try {
        const response = await fetch(`${API_BASE}/training/status`);
        const data = await response.json();

        console.log('Training status response:', data);

        if (data.running) {
            updateTrainingStats(data);
            document.getElementById('trainingStatus').textContent = 'Training Running';
            document.getElementById('trainingStatus').style.color = 'blue';
            document.getElementById('startTraining').disabled = true;
            document.getElementById('stopTraining').disabled = false;
        } else if (data.episode > 0) {
            // Training completed but UI needs update
            document.getElementById('trainingStatus').textContent = 'Training Complete';
            document.getElementById('trainingStatus').style.color = 'green';
            document.getElementById('startTraining').disabled = false;
            document.getElementById('stopTraining').disabled = true;

            // Update final stats with correct progress calculation
            document.getElementById('episode').textContent = data.episode;
            document.getElementById('score').textContent = data.score || 0;
            document.getElementById('avgScore').textContent = data.avg_score?.toFixed(1) || '0.0';
            document.getElementById('epsilon').textContent = data.epsilon?.toFixed(4) || '0.0000';

            // Update progress correctly
            const totalEpisodes = 1000;
            const progressPercent = (data.episode / totalEpisodes) * 100;
            document.getElementById('trainingProgress').value = data.episode;
            document.getElementById('progressText').textContent = `${progressPercent.toFixed(1)}% (${data.episode}/${totalEpisodes})`;

            // Try to load final replay
            loadFinalReplay();
        } else {
            document.getElementById('trainingStatus').textContent = 'Ready';
            document.getElementById('trainingStatus').style.color = 'gray';
        }
    } catch (error) {
        console.error('Status check error:', error);
        document.getElementById('trainingStatus').textContent = 'Connection Error';
        document.getElementById('trainingStatus').style.color = 'red';
    }
}

// Removed all socket events - using REST API only

function handleReplayData(data) {
    try {
        // Ensure states are in proper format
        gameStates = Array.isArray(data.states[0]) ?
            data.states :
            data.states.map(state => Array.isArray(state) ? state : state.tolist());

        currentFrame = 0;
        updateFrameCounter();
        renderGameState(gameStates[currentFrame]);

        // Show evaluation if available
        if (data.evaluation) {
            showFinalEvaluation(data.evaluation);
        }

        // Enable controls
        document.getElementById('playPause').disabled = false;
        document.getElementById('prevFrame').disabled = false;
        document.getElementById('nextFrame').disabled = false;

        console.log(`Loaded ${gameStates.length} frames for replay`);

    } catch (error) {
        console.error('Error handling replay data:', error);
        document.getElementById('errorMessage').textContent =
            `Replay error: ${error.message}`;
    }
}

async function loadFinalReplayFallback() {
    try {
        console.log('Trying fallback replay endpoint');
        const response = await fetch(`${API_BASE}/final_replay`);
        const data = await response.json();
        if (data.error) throw new Error(data.error);
        handleReplayData(data);
    } catch (error) {
        console.error('Fallback replay failed:', error);
        document.getElementById('errorMessage').textContent =
            `Failed to load replay: ${error.message}`;
    }
}

async function loadFinalEvaluationFallback() {
    try {
        console.log('Trying fallback evaluation endpoint');
        const response = await fetch(`${API_BASE}/final_evaluation`);
        const data = await response.json();
        if (data.error) throw new Error(data.error);
        showFinalEvaluation(data.evaluation);
    } catch (error) {
        console.error('Fallback evaluation failed:', error);
        document.getElementById('errorMessage').textContent =
            `Failed to load evaluation: ${error.message}`;
    }
}

// Evaluation section
const evaluationSection = document.createElement('div');
evaluationSection.id = 'evaluationSection';
evaluationSection.innerHTML = `
    <h2>Model Evaluation</h2>
    <div class="eval-metrics">
        <div class="metric">
            <span class="metric-label">Mean Score:</span>
            <span id="finalMeanScore" class="metric-value">0</span>
        </div>
        <div class="metric">
            <span class="metric-label">Max Score:</span>
            <span id="finalMaxScore" class="metric-value">0</span>
        </div>
        <div class="metric">
            <span class="metric-label">Avg Steps:</span>
            <span id="finalSteps" class="metric-value">0</span>
        </div>
    </div>
    <h3>Action Distribution</h3>
    <div class="action-distribution">
        <div class="action">
            <span class="action-label">Left:</span>
            <span id="finalActionLeft" class="action-value">0%</span>
        </div>
        <div class="action">
            <span class="action-label">Right:</span>
            <span id="finalActionRight" class="action-value">0%</span>
        </div>
        <div class="action">
            <span class="action-label">Straight:</span>
            <span id="finalActionStraight" class="action-value">0%</span>
        </div>
    </div>
`;
document.querySelector('.container').appendChild(evaluationSection);

function showFinalEvaluation(evalData) {
    // Ensure evaluation section is visible
    document.getElementById('evaluationSection').style.display = 'block';

    // Update metrics
    document.getElementById('finalMeanScore').textContent =
        evalData.mean_score?.toFixed(1) || 'N/A';
    document.getElementById('finalMaxScore').textContent =
        evalData.max_score || 'N/A';
    document.getElementById('finalSteps').textContent =
        evalData.steps?.toFixed(1) || 'N/A';

    // Format action distribution
    if (evalData.action_distribution) {
        const actions = evalData.action_distribution;
        document.getElementById('finalActionLeft').textContent =
            `${(actions.left * 100).toFixed(1)}%`;
        document.getElementById('finalActionRight').textContent =
            `${(actions.right * 100).toFixed(1)}%`;
        document.getElementById('finalActionStraight').textContent =
            `${(actions.straight * 100).toFixed(1)}%`;
    }

    // Scroll to evaluation section
    document.getElementById('evaluationSection').scrollIntoView({
        behavior: 'smooth'
    });
}

// Initialize game canvas
function initCanvas() {
    canvas.width = GRID_SIZE * CELL_SIZE;
    canvas.height = GRID_SIZE * CELL_SIZE;
    ctx.fillStyle = COLORS.background;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
}

// Render game state
function renderGameState(state) {
    // Clear canvas
    ctx.fillStyle = COLORS.background;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw grid
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    for (let i = 0; i <= GRID_SIZE; i++) {
        // Vertical lines
        ctx.beginPath();
        ctx.moveTo(i * CELL_SIZE, 0);
        ctx.lineTo(i * CELL_SIZE, GRID_SIZE * CELL_SIZE);
        ctx.stroke();

        // Horizontal lines
        ctx.beginPath();
        ctx.moveTo(0, i * CELL_SIZE);
        ctx.lineTo(GRID_SIZE * CELL_SIZE, i * CELL_SIZE);
        ctx.stroke();
    }

    // Draw snake and food
    for (let i = 0; i < GRID_SIZE; i++) {
        for (let j = 0; j < GRID_SIZE; j++) {
            const value = state[i][j];

            if (value === 1.0) { // Snake head
                ctx.fillStyle = COLORS.snakeHead;
                ctx.fillRect(j * CELL_SIZE, i * CELL_SIZE, CELL_SIZE, CELL_SIZE);
            } else if (value === 0.5) { // Snake body
                ctx.fillStyle = COLORS.snakeBody;
                ctx.fillRect(j * CELL_SIZE, i * CELL_SIZE, CELL_SIZE, CELL_SIZE);
            } else if (value === -1.0) { // Food
                ctx.fillStyle = COLORS.food;
                ctx.beginPath();
                ctx.arc(
                    j * CELL_SIZE + CELL_SIZE/2,
                    i * CELL_SIZE + CELL_SIZE/2,
                    CELL_SIZE/2 - 2,
                    0,
                    Math.PI * 2
                );
                ctx.fill();
            }
        }
    }
}

function loadFinalReplay() {
    console.log('Loading final replay via REST API');
    loadFinalReplayFallback();
}

async function loadReplay(episode) {
    try {
        console.log(`Loading replay for episode ${episode}`);
        const response = await fetch(`${API_BASE}/replay/${episode}`);
        const data = await response.json();

        if (data.error) {
            console.error('Replay error:', data.error);
            document.getElementById('replayStatus').textContent = 'Replay failed';
            document.getElementById('replayStatus').style.color = 'red';
            return;
        }

        handleReplayData(data);
        document.getElementById('replayStatus').textContent = `Episode ${episode} loaded`;
        document.getElementById('replayStatus').style.color = 'green';
    } catch (error) {
        console.error('Error loading replay:', error);
        document.getElementById('errorMessage').textContent = `Replay error: ${error.message}`;
    }
}

// Using REST API only - no connection checks needed

// Replay controls
function updateFrameCounter() {
    frameCounter.textContent = `Frame: ${currentFrame + 1}/${gameStates.length}`;
}

function showNextFrame() {
    if (currentFrame < gameStates.length - 1) {
        currentFrame++;
        renderGameState(gameStates[currentFrame]);
        updateFrameCounter();
    }
}

function showPrevFrame() {
    if (currentFrame > 0) {
        currentFrame--;
        renderGameState(gameStates[currentFrame]);
        updateFrameCounter();
    }
}

function togglePlayPause() {
    isPlaying = !isPlaying;

    if (isPlaying) {
        playPauseBtn.textContent = 'Pause';
        playInterval = setInterval(showNextFrame, 1000 / playSpeed);
    } else {
        playPauseBtn.textContent = 'Play';
        clearInterval(playInterval);
    }
}

// Training controls
function updateTrainingStats(data) {
    try {
        console.log('Training update:', data);

        // Update episode and scores
        episodeEl.textContent = data.episode || 0;
        scoreEl.textContent = data.score || 0;
        avgScoreEl.textContent = data.avg_score?.toFixed(1) || '0.0';
        epsilonEl.textContent = data.epsilon?.toFixed(4) || '0.0000';

        // Update progress bar correctly - FIXED
        const episode = data.episode || 0;
        const totalEpisodes = 1000;
        progressBar.max = totalEpisodes;
        progressBar.value = episode;
        const progressPercent = (episode / totalEpisodes) * 100;
        progressText.textContent = `${progressPercent.toFixed(1)}% (${episode}/${totalEpisodes})`;

        // Update training controls
        stopTrainingBtn.disabled = false;

        // Update evaluation metrics if available
        if (data.evaluation) {
            document.getElementById('evalMeanScore').textContent =
                data.evaluation.mean_score?.toFixed(1) || 'N/A';
            document.getElementById('evalMaxScore').textContent =
                data.evaluation.max_score || 'N/A';
            document.getElementById('evalSteps').textContent =
                data.evaluation.steps?.toFixed(1) || 'N/A';

            // Format action distribution
            if (data.evaluation.action_distribution) {
                const actions = data.evaluation.action_distribution;
                document.getElementById('actionLeft').textContent =
                    `${(actions.left * 100).toFixed(1)}%`;
                document.getElementById('actionRight').textContent =
                    `${(actions.right * 100).toFixed(1)}%`;
                document.getElementById('actionStraight').textContent =
                    `${(actions.straight * 100).toFixed(1)}%`;
            }
        }

        // Load replay if available
        if (data.has_replay) {
            console.log(`Loading replay for episode ${data.episode}`);
            loadReplay(data.episode);
        }
    } catch (error) {
        console.error('Error updating training stats:', error);
        document.getElementById('errorMessage').textContent =
            `UI update error: ${error.message}`;
    }
}

async function startTraining() {
    try {
        const response = await fetch(`${API_BASE}/training/start`, {
            method: 'POST'
        });
        const data = await response.json();
        console.log(data.status);

        startTrainingBtn.disabled = true;
        stopTrainingBtn.disabled = false;
    } catch (error) {
        console.error('Error starting training:', error);
    }
}

async function stopTraining() {
    try {
        const response = await fetch(`${API_BASE}/training/stop`, {
            method: 'POST'
        });
        const data = await response.json();
        console.log(data.status);

        startTrainingBtn.disabled = false;
        stopTrainingBtn.disabled = true;
    } catch (error) {
        console.error('Error stopping training:', error);
    }
}

async function evaluateModel() {
    console.log('Evaluate button clicked!');
    try {
        evaluateBtn.disabled = true;
        evaluateBtn.textContent = 'Evaluating...';

        console.log('Fetching evaluation from:', `${API_BASE}/final_evaluation`);
        const response = await fetch(`${API_BASE}/final_evaluation`);
        const data = await response.json();

        console.log('Evaluation response:', data);

        if (data.error) {
            throw new Error(data.error);
        }

        // Show evaluation results
        showFinalEvaluation(data.evaluation);

        // Also try to load replay
        loadFinalReplay();

        evaluateBtn.textContent = 'Evaluate Model';
        evaluateBtn.disabled = false;
    } catch (error) {
        console.error('Error evaluating model:', error);
        const errorEl = document.getElementById('errorMessage');
        if (errorEl) {
            errorEl.textContent = `Evaluation error: ${error.message}`;
        }
        evaluateBtn.textContent = 'Evaluate Model';
        evaluateBtn.disabled = false;
    }
}

// Event listeners
document.getElementById('loadFinalReplay').addEventListener('click', loadFinalReplay);
prevFrameBtn.addEventListener('click', showPrevFrame);
nextFrameBtn.addEventListener('click', showNextFrame);
playPauseBtn.addEventListener('click', togglePlayPause);
speedControl.addEventListener('input', (e) => {
    playSpeed = parseInt(e.target.value);
    if (isPlaying) {
        clearInterval(playInterval);
        playInterval = setInterval(showNextFrame, 1000 / playSpeed);
    }
});
startTrainingBtn.addEventListener('click', startTraining);
stopTrainingBtn.addEventListener('click', stopTraining);
evaluateBtn.addEventListener('click', evaluateModel);

// Initialize
initCanvas();

// Simple initialization

// Check training status on load
(async function() {
    try {
        console.log('Initial status check...');
        const response = await fetch(`${API_BASE}/training/status`);
        const data = await response.json();

        console.log('Initial status response:', data);

        if (data.running) {
            startTrainingBtn.disabled = true;
            stopTrainingBtn.disabled = false;
            updateTrainingStats(data);
        } else {
            // Update with current data even if not running
            fetchTrainingStatus();
        }
    } catch (error) {
        console.error('Error checking training status:', error);
    }
})();
