// Game rendering constants
const GRID_SIZE = 10;
const CELL_SIZE = 40;
const COLORS = {
    background: '#222',
    snakeHead: '#2ecc71',
    snakeBody: '#27ae60',
    food: '#e74c3c',
    text: '#ecf0f1'
};

// Game state
let currentEpisode = null;
let gameStates = [];
let currentFrame = 0;
let isPlaying = false;
let playInterval = null;
let playSpeed = 5;

// DOM elements
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const frameCounter = document.getElementById('frameCounter');
const prevFrameBtn = document.getElementById('prevFrame');
const nextFrameBtn = document.getElementById('nextFrame');
const playPauseBtn = document.getElementById('playPause');
const speedControl = document.getElementById('speedControl');
const startTrainingBtn = document.getElementById('startTraining');
const stopTrainingBtn = document.getElementById('stopTraining');
const episodeEl = document.getElementById('episode');
const scoreEl = document.getElementById('score');
const avgScoreEl = document.getElementById('avgScore');
const epsilonEl = document.getElementById('epsilon');
const progressBar = document.getElementById('trainingProgress');
const progressText = document.getElementById('progressText');

// Initialize WebSocket connection
const socket = io('http://localhost', { path: '/socket.io' });

let useWebSocket = true;

// Socket event handlers
socket.on('connect', () => {
    console.log('Connected to WebSocket');
    document.getElementById('connectionStatus').textContent = 'Connected';
    document.getElementById('connectionStatus').style.color = 'green';
});

socket.on('disconnect', () => {
    console.log('Disconnected from WebSocket');
    document.getElementById('connectionStatus').textContent = 'Disconnected';
    document.getElementById('connectionStatus').style.color = 'red';
    useWebSocket = false;
});

socket.on('training_update', (data) => {
    updateTrainingStats(data);
});

// Auto-refresh interval
let refreshInterval = setInterval(() => {
    if (document.hidden) return;
    fetchTrainingStatus();
}, 2000);

socket.on('training_complete', (data) => {
    console.log('Training complete:', data);
    
    // Update all stats
    updateTrainingStats(data);
    showFinalEvaluation(data.evaluation);
    
    // Update UI state
    document.getElementById('trainingStatus').textContent = 'Training Complete';
    document.getElementById('trainingStatus').style.color = 'green';
    document.getElementById('startTraining').disabled = false;
    document.getElementById('stopTraining').disabled = true;
    
    // Clear auto-refresh
    clearInterval(refreshInterval);
    
    // Force UI update
    fetchTrainingStatus();
});

async function fetchTrainingStatus() {
    try {
        const response = await fetch(`${API_BASE}/training/status`);
        const data = await response.json();
        
        if (data.running) {
            updateTrainingStats(data);
        } else if (data.episode > 0) {
            // Training completed but UI needs update
            document.getElementById('trainingStatus').textContent = 'Training Complete';
            document.getElementById('trainingStatus').style.color = 'green';
        }
    } catch (error) {
        console.error('Status check error:', error);
    }
}

socket.on('trigger_replay', () => {
    console.log('Triggering final replay');
    loadFinalReplay();
});

socket.on('replay_ready', (data) => {
    console.log('Replay ready:', data);
    handleReplayData(data);
    document.getElementById('replayStatus').textContent = 'Replay Loaded';
    document.getElementById('replayStatus').style.color = 'green';
});

socket.on('training_error', (error) => {
    console.error('Training error:', error);
    const timestamp = error.time || error.timestamp || 'unknown time';
    document.getElementById('errorMessage').textContent = 
        `[${timestamp}] Training error (episode ${error.episode || 0}): ${error.error}`;
    document.getElementById('trainingStatus').textContent = 'Training Failed';
    document.getElementById('trainingStatus').style.color = 'red';
    
    // Reset UI state
    document.getElementById('startTraining').disabled = false;
    document.getElementById('stopTraining').disabled = true;
    document.getElementById('episode').textContent = error.episode || 0;
    document.getElementById('score').textContent = 0;
    document.getElementById('avgScore').textContent = '0.0';
    
    // Force immediate status check
    setTimeout(fetchTrainingStatus, 500);
});

socket.on('replay_error', (error) => {
    console.error('Replay error:', error);
    document.getElementById('errorMessage').textContent = `Replay error: ${error.error}`;
    loadFinalReplayFallback();
});

function handleReplayData(data) {
    try {
        // Ensure states are in proper format
        gameStates = Array.isArray(data.states[0]) ? 
            data.states : 
            data.states.map(state => Array.isArray(state) ? state : state.tolist());
            
        currentFrame = 0;
        updateFrameCounter();
        renderGameState(gameStates[currentFrame]);
        
        // Show evaluation if available
        if (data.evaluation) {
            showFinalEvaluation(data.evaluation);
        }
        
        // Enable controls
        document.getElementById('playPause').disabled = false;
        document.getElementById('prevFrame').disabled = false;
        document.getElementById('nextFrame').disabled = false;
        
    } catch (error) {
        console.error('Error handling replay data:', error);
        document.getElementById('errorMessage').textContent = 
            `Replay error: ${error.message}`;
    }
}

async function loadFinalReplayFallback() {
    try {
        console.log('Trying fallback replay endpoint');
        const response = await fetch(`${API_BASE}/final_replay`);
        const data = await response.json();
        if (data.error) throw new Error(data.error);
        handleReplayData(data);
    } catch (error) {
        console.error('Fallback replay failed:', error);
        document.getElementById('errorMessage').textContent = 
            `Failed to load replay: ${error.message}`;
    }
}

async function loadFinalEvaluationFallback() {
    try {
        console.log('Trying fallback evaluation endpoint');
        const response = await fetch(`${API_BASE}/final_evaluation`);
        const data = await response.json();
        if (data.error) throw new Error(data.error);
        showFinalEvaluation(data.evaluation);
    } catch (error) {
        console.error('Fallback evaluation failed:', error);
        document.getElementById('errorMessage').textContent = 
            `Failed to load evaluation: ${error.message}`;
    }
}

// Evaluation section
const evaluationSection = document.createElement('div');
evaluationSection.id = 'evaluationSection';
evaluationSection.innerHTML = `
    <h2>Model Evaluation</h2>
    <div class="eval-metrics">
        <div class="metric">
            <span class="metric-label">Mean Score:</span>
            <span id="finalMeanScore" class="metric-value">0</span>
        </div>
        <div class="metric">
            <span class="metric-label">Max Score:</span>
            <span id="finalMaxScore" class="metric-value">0</span>
        </div>
        <div class="metric">
            <span class="metric-label">Avg Steps:</span>
            <span id="finalSteps" class="metric-value">0</span>
        </div>
    </div>
    <h3>Action Distribution</h3>
    <div class="action-distribution">
        <div class="action">
            <span class="action-label">Left:</span>
            <span id="finalActionLeft" class="action-value">0%</span>
        </div>
        <div class="action">
            <span class="action-label">Right:</span>
            <span id="finalActionRight" class="action-value">0%</span>
        </div>
        <div class="action">
            <span class="action-label">Straight:</span>
            <span id="finalActionStraight" class="action-value">0%</span>
        </div>
    </div>
`;
document.querySelector('.container').appendChild(evaluationSection);

function showFinalEvaluation(evalData) {
    // Ensure evaluation section is visible
    document.getElementById('evaluationSection').style.display = 'block';
    
    // Update metrics
    document.getElementById('finalMeanScore').textContent = 
        evalData.mean_score?.toFixed(1) || 'N/A';
    document.getElementById('finalMaxScore').textContent = 
        evalData.max_score || 'N/A';
    document.getElementById('finalSteps').textContent = 
        evalData.steps?.toFixed(1) || 'N/A';
    
    // Format action distribution
    if (evalData.action_distribution) {
        const actions = evalData.action_distribution;
        document.getElementById('finalActionLeft').textContent = 
            `${(actions.left * 100).toFixed(1)}%`;
        document.getElementById('finalActionRight').textContent = 
            `${(actions.right * 100).toFixed(1)}%`;
        document.getElementById('finalActionStraight').textContent = 
            `${(actions.straight * 100).toFixed(1)}%`;
    }
    
    // Scroll to evaluation section
    document.getElementById('evaluationSection').scrollIntoView({
        behavior: 'smooth'
    });
}

// Initialize game canvas
function initCanvas() {
    canvas.width = GRID_SIZE * CELL_SIZE;
    canvas.height = GRID_SIZE * CELL_SIZE;
    ctx.fillStyle = COLORS.background;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
}

// Render game state
function renderGameState(state) {
    // Clear canvas
    ctx.fillStyle = COLORS.background;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Draw grid
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    for (let i = 0; i <= GRID_SIZE; i++) {
        // Vertical lines
        ctx.beginPath();
        ctx.moveTo(i * CELL_SIZE, 0);
        ctx.lineTo(i * CELL_SIZE, GRID_SIZE * CELL_SIZE);
        ctx.stroke();
        
        // Horizontal lines
        ctx.beginPath();
        ctx.moveTo(0, i * CELL_SIZE);
        ctx.lineTo(GRID_SIZE * CELL_SIZE, i * CELL_SIZE);
        ctx.stroke();
    }
    
    // Draw snake and food
    for (let i = 0; i < GRID_SIZE; i++) {
        for (let j = 0; j < GRID_SIZE; j++) {
            const value = state[i][j];
            
            if (value === 1.0) { // Snake head
                ctx.fillStyle = COLORS.snakeHead;
                ctx.fillRect(j * CELL_SIZE, i * CELL_SIZE, CELL_SIZE, CELL_SIZE);
            } else if (value === 0.5) { // Snake body
                ctx.fillStyle = COLORS.snakeBody;
                ctx.fillRect(j * CELL_SIZE, i * CELL_SIZE, CELL_SIZE, CELL_SIZE);
            } else if (value === -1.0) { // Food
                ctx.fillStyle = COLORS.food;
                ctx.beginPath();
                ctx.arc(
                    j * CELL_SIZE + CELL_SIZE/2, 
                    i * CELL_SIZE + CELL_SIZE/2, 
                    CELL_SIZE/2 - 2, 
                    0, 
                    Math.PI * 2
                );
                ctx.fill();
            }
        }
    }
}

// Load game states for replay
const API_BASE = '/api';

function loadFinalReplay() {
    if (useWebSocket && socket.connected) {
        console.log('Requesting final model replay via WebSocket');
        socket.emit('request_final_replay');
    } else {
        console.log('WebSocket not available, using fallback');
        loadFinalReplayFallback();
    }
}

// Check connection status periodically
setInterval(() => {
    if (!socket.connected && useWebSocket) {
        console.log('WebSocket disconnected, switching to fallback');
        useWebSocket = false;
        document.getElementById('connectionStatus').textContent = 'Using fallback';
        document.getElementById('connectionStatus').style.color = 'orange';
    }
}, 5000);

// Replay controls
function updateFrameCounter() {
    frameCounter.textContent = `Frame: ${currentFrame + 1}/${gameStates.length}`;
}

function showNextFrame() {
    if (currentFrame < gameStates.length - 1) {
        currentFrame++;
        renderGameState(gameStates[currentFrame]);
        updateFrameCounter();
    }
}

function showPrevFrame() {
    if (currentFrame > 0) {
        currentFrame--;
        renderGameState(gameStates[currentFrame]);
        updateFrameCounter();
    }
}

function togglePlayPause() {
    isPlaying = !isPlaying;
    
    if (isPlaying) {
        playPauseBtn.textContent = 'Pause';
        playInterval = setInterval(showNextFrame, 1000 / playSpeed);
    } else {
        playPauseBtn.textContent = 'Play';
        clearInterval(playInterval);
    }
}

// Training controls
function updateTrainingStats(data) {
    try {
        console.log('Training update:', data);
        
        // Update episode and scores
        episodeEl.textContent = data.episode || 0;
        scoreEl.textContent = data.score || 0;
        avgScoreEl.textContent = data.avg_score?.toFixed(1) || '0.0';
        epsilonEl.textContent = data.epsilon?.toFixed(4) || '0.0000';
        
        // Update progress bar correctly
        const totalEpisodes = 100000;
        progressBar.max = totalEpisodes;
        progressBar.value = data.episode || 0;
        const progressPercent = ((data.episode || 0) / totalEpisodes) * 100;
        progressText.textContent = `${progressPercent.toFixed(1)}%`;
        
        // Update training controls
        stopTrainingBtn.disabled = false;
        
        // Update evaluation metrics if available
        if (data.evaluation) {
            document.getElementById('evalMeanScore').textContent = 
                data.evaluation.mean_score?.toFixed(1) || 'N/A';
            document.getElementById('evalMaxScore').textContent = 
                data.evaluation.max_score || 'N/A';
            document.getElementById('evalSteps').textContent = 
                data.evaluation.steps?.toFixed(1) || 'N/A';
            
            // Format action distribution
            if (data.evaluation.action_distribution) {
                const actions = data.evaluation.action_distribution;
                document.getElementById('actionLeft').textContent = 
                    `${(actions.left * 100).toFixed(1)}%`;
                document.getElementById('actionRight').textContent = 
                    `${(actions.right * 100).toFixed(1)}%`;
                document.getElementById('actionStraight').textContent = 
                    `${(actions.straight * 100).toFixed(1)}%`;
            }
        }
        
        // Load replay if available
        if (data.has_replay) {
            console.log(`Loading replay for episode ${data.episode}`);
            loadReplay(data.episode);
        }
    } catch (error) {
        console.error('Error updating training stats:', error);
        document.getElementById('errorMessage').textContent = 
            `UI update error: ${error.message}`;
    }
}

async function startTraining() {
    try {
        const response = await fetch(`${API_BASE}/training/start`, {
            method: 'POST'
        });
        const data = await response.json();
        console.log(data.status);
        
        startTrainingBtn.disabled = true;
        stopTrainingBtn.disabled = false;
    } catch (error) {
        console.error('Error starting training:', error);
    }
}

async function stopTraining() {
    try {
        const response = await fetch(`${API_BASE}/training/stop`, {
            method: 'POST'
        });
        const data = await response.json();
        console.log(data.status);
        
        startTrainingBtn.disabled = false;
        stopTrainingBtn.disabled = true;
    } catch (error) {
        console.error('Error stopping training:', error);
    }
}

// Event listeners
prevFrameBtn.addEventListener('click', showPrevFrame);
nextFrameBtn.addEventListener('click', showNextFrame);
playPauseBtn.addEventListener('click', togglePlayPause);
speedControl.addEventListener('input', (e) => {
    playSpeed = parseInt(e.target.value);
    if (isPlaying) {
        clearInterval(playInterval);
        playInterval = setInterval(showNextFrame, 1000 / playSpeed);
    }
});
startTrainingBtn.addEventListener('click', startTraining);
stopTrainingBtn.addEventListener('click', stopTraining);

// Initialize
initCanvas();

// Check training status on load
(async function() {
    try {
        const response = await fetch(`${API_BASE}/training/status`);
        const data = await response.json();
        
        if (data.running) {
            startTrainingBtn.disabled = true;
            stopTrainingBtn.disabled = false;
            updateTrainingStats(data);
        }
    } catch (error) {
        console.error('Error checking training status:', error);
    }
})();
