import time
from datetime import datetime
from flask import Flask, jsonify, request
from flask_socketio import <PERSON><PERSON><PERSON>
from threading import Thread, Event
import numpy as np
import os
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('snake_rl.log')
    ]
)

# Import game components
from snake_game import <PERSON><PERSON><PERSON>
from agent import RLA<PERSON>
from evaluation import Evaluator

app = Flask(__name__)
socketio = SocketIO(app, cors_allowed_origins="*")

# Training thread control
training_thread = None
stop_training = Event()

class WebTrainer:
    def __init__(self):
        self.model_path = "models/snake_agent.npy"
        self.env = SnakeGame()
        self.agent = RLAgent(state_size=(10, 10), action_size=3)

        # Load or initialize model
        if os.path.exists(self.model_path):
            self.agent.load(self.model_path)

        # Training stats
        self.episode = 0
        self.scores = []
        self.avg_scores = []

    def train(self):
        """Training loop to run in background thread"""
        import time  # Explicit import
        from config import Config

        # Store recent game states for replay
        self.replay_states = {}
        logging.info("Starting training loop")

        while not stop_training.is_set() and self.episode < Config.EPISODES:
            logging.info(f"Starting episode {self.episode + 1}")
            state = self.env.reset()
            episode_states = [state.copy()]
            done = False
            score = 0

            while not done:
                action = self.agent.act(state)
                next_state, reward, done, _ = self.env.step(action)
                self.agent.remember(state, action, reward, next_state, done)
                state = next_state
                episode_states.append(state.copy())
                score += reward

                if len(self.agent.memory) > Config.BATCH_SIZE:
                    self.agent.replay(Config.BATCH_SIZE)

            self.scores.append(score)
            avg_score = np.mean(self.scores[-100:])
            self.avg_scores.append(avg_score)
            self.episode += 1

            # Store states for replay (keep last 10 episodes)
            self.replay_states[self.episode] = episode_states
            logging.info(f"Stored replay states for episode {self.episode}")
            if len(self.replay_states) > 10:
                oldest = min(self.replay_states.keys())
                del self.replay_states[oldest]
                logging.info(f"Removed replay states for episode {oldest}")

            # Emit training progress (only every 10 episodes to avoid spam)
            if self.episode % 10 == 0:
                socketio.emit('training_update', {
                    'episode': self.episode,
                    'score': score,
                    'avg_score': avg_score,
                    'epsilon': self.agent.epsilon,
                    'has_replay': False
                })

            # Save model periodically
            if self.episode % Config.SAVE_INTERVAL == 0:
                self.agent.save(self.model_path)

        try:
            # Save final model and run evaluation
            self.agent.save(self.model_path)
            evaluator = Evaluator(self.model_path)
            final_eval = evaluator.evaluate(num_episodes=10, render=False)

            # Store final replay states (already converted to lists in evaluator)
            if hasattr(evaluator, 'last_episode_states'):
                self.replay_states['final'] = evaluator.last_episode_states

            # Send final evaluation results
            socketio.emit('training_complete', {
                'episode': self.episode,
                'evaluation': {
                    'mean_score': float(final_eval['mean_score']),
                    'max_score': float(final_eval['max_score']),
                    'steps': float(final_eval['mean_steps']),
                    'action_distribution': {
                        'left': float(final_eval['action_distribution']['left']),
                        'right': float(final_eval['action_distribution']['right']),
                        'straight': float(final_eval['action_distribution']['straight'])
                    }
                },
                'has_replay': True
            }, callback=lambda: socketio.emit('trigger_replay'))
            logging.info('Training complete event emitted')

        except Exception as e:
            logging.error(f'Training completion error: {str(e)}')
            try:
                current_time = datetime.now().isoformat()
            except Exception:
                current_time = "unknown"

            socketio.emit('training_error', {
                'error': str(e),
                'timestamp': current_time,
                'episode': self.episode,
                'completed': False,
                'time': current_time
            })

            # Ensure training is properly stopped
            stop_training.set()
            # Ensure training is properly stopped
            stop_training.set()

# Initialize trainer and evaluation handler
trainer = WebTrainer()

@socketio.on('connect')
def handle_connect():
    logging.info(f'Client connected: {request.sid}')

@socketio.on('disconnect')
def handle_disconnect():
    logging.info(f'Client disconnected: {request.sid}')

@socketio.on('request_final_replay')
def handle_final_replay():
    """Handle request for final model replay"""
    try:
        logging.info('Starting final replay evaluation')
        evaluator = Evaluator(trainer.model_path)
        final_eval = evaluator.evaluate(num_episodes=1, render=False)

        if hasattr(evaluator, 'last_episode_states'):
            trainer.replay_states['final'] = evaluator.last_episode_states
            socketio.emit('replay_ready', {
                'states': evaluator.last_episode_states,
                'evaluation': {
                    'mean_score': final_eval['mean_score'],
                    'max_score': final_eval['max_score'],
                    'steps': final_eval['mean_steps'],
                    'action_distribution': final_eval['action_distribution']
                }
            }, callback=ack_replay)
            logging.info('Replay data emitted')
    except Exception as e:
        logging.error(f'Replay error: {str(e)}')
        socketio.emit('replay_error', {'error': str(e)})

def ack_replay():
    logging.info('Replay data received by client')

@app.route('/api/final_evaluation')
def get_final_evaluation():
    """Fallback endpoint for final evaluation"""
    try:
        evaluator = Evaluator(trainer.model_path)
        final_eval = evaluator.evaluate(num_episodes=10, render=False)
        return jsonify({
            'evaluation': {
                'mean_score': final_eval['mean_score'],
                'max_score': final_eval['max_score'],
                'steps': final_eval['mean_steps'],
                'action_distribution': final_eval['action_distribution']
            }
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/final_replay')
def get_final_replay():
    """Fallback endpoint for final replay"""
    try:
        evaluator = Evaluator(trainer.model_path)
        final_eval = evaluator.evaluate(num_episodes=1, render=False)

        if hasattr(evaluator, 'last_episode_states'):
            return jsonify({
                'states': evaluator.last_episode_states,
                'evaluation': {
                    'mean_score': final_eval['mean_score'],
                    'max_score': final_eval['max_score'],
                    'steps': final_eval['mean_steps'],
                    'action_distribution': final_eval['action_distribution']
                }
            })
        return jsonify({'error': 'No replay data'}), 404
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/replay/<int:episode>')
def get_replay(episode):
    """Get game states for a specific episode"""
    logging.info(f"Requested replay for episode {episode}")
    if episode in trainer.replay_states:
        logging.info(f"Found replay data for episode {episode}")
        # Convert numpy arrays to lists for JSON serialization
        states = [state.tolist() for state in trainer.replay_states[episode]]
        return jsonify({
            'states': states,
            'score': trainer.scores[episode-1],
            'steps': len(trainer.replay_states[episode])
        })
    else:
        logging.warning(f"No replay data available for episode {episode}")
        return jsonify({'error': 'Replay not available'}), 404

@app.route('/api/training/start', methods=['POST'])
def start_training():
    """Start training in background thread"""
    global training_thread
    if training_thread is None or not training_thread.is_alive():
        stop_training.clear()
        training_thread = Thread(target=trainer.train)
        training_thread.start()
        return jsonify({'status': 'training started'})
    return jsonify({'status': 'training already running'})

@app.route('/api/training/stop', methods=['POST'])
def stop_training_endpoint():
    """Stop training"""
    stop_training.set()
    return jsonify({'status': 'training stopped'})

@app.route('/api/training/status')
def training_status():
    """Get current training status"""
    return jsonify({
        'running': training_thread is not None and training_thread.is_alive(),
        'episode': trainer.episode,
        'score': trainer.scores[-1] if trainer.scores else 0,
        'avg_score': trainer.avg_scores[-1] if trainer.avg_scores else 0,
        'epsilon': trainer.agent.epsilon
    })

if __name__ == '__main__':
    # Create required directories
    Path("models").mkdir(exist_ok=True)
    Path("plots").mkdir(exist_ok=True)

    # Start Flask app
    socketio.run(app, host='0.0.0.0', port=5000)
