version: '3.8'

services:
  backend:
    build: .
    container_name: snake_rl_backend
    restart: unless-stopped
    volumes:
      - ./models:/app/models
      - ./plots:/app/plots
      - ./web/static:/app/web/static
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
    command: python web/app.py

  frontend:
    image: nginx:alpine
    container_name: snake_rl_frontend
    restart: unless-stopped
    volumes:
      - ./web/static:/usr/share/nginx/html
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  models:
  plots:
  static:
