import numpy as np
import os
import json
from typing import Dict, Any
import time
from datetime import datetime

def create_dir(path: str) -> str:
    """Create directory if it doesn't exist"""
    os.makedirs(path, exist_ok=True)
    return path

def timestamp() -> str:
    """Get current timestamp string"""
    return datetime.now().strftime("%Y%m%d-%H%M%S")

def save_config(config: Dict[str, Any], path: str):
    """Save configuration dictionary to JSON file"""
    with open(path, 'w') as f:
        json.dump(config, f, indent=4)

def load_config(path: str) -> Dict[str, Any]:
    """Load configuration from JSON file"""
    with open(path, 'r') as f:
        return json.load(f)

def moving_average(data: list, window_size: int = 10) -> list:
    """Calculate moving average"""
    return [
        np.mean(data[max(0, i - window_size):i+1])
        for i in range(len(data))
    ]

def normalize_state(state: np.ndarray) -> np.ndarray:
    """Normalize game state to [-1, 1] range"""
    return (state - 0.5) * 2

def log_message(message: str, log_file: str = "training.log"):
    """Log message with timestamp"""
    with open(log_file, 'a') as f:
        f.write(f"[{timestamp()}] {message}\n")
