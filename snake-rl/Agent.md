# Snake Game RL Agent

## 🎯 Projektbeschreibung

Ein Snake-Spiel Environment speziell entwickelt für Reinforcement Learning Experimente. Das Spiel implementiert eine CLI-basierte Umgebung ohne GUI für maximale Performance und schnelle Trainingsiterationen.

## 🎮 Game Environment

### Spielfeld Spezifikationen
- **Dimensionen**: 10x10 Grid
- **Interface**: Command Line Interface (CLI)
- **Rendering**: ASCII-basierte Darstellung
- **Performance**: Optimiert für schnelle RL-Trainingszyklen

### Bewegungsmechanik
Das Spiel implementiert ein **relatives Bewegungssystem** mit 3 möglichen Aktionen während der Bewegung:

| Aktion | Index | Beschreibung |
|--------|-------|--------------|
| Links drehen | 0 | Richtungsänderung um 90° nach links (relativ) |
| Geradeaus | 1 | Beibehaltung der aktuellen Bewegungsrichtung |
| Rechts drehen | 2 | Richtungsänderung um 90° nach rechts (relativ) |

## 🧠 Reinforcement Learning Setup

### State Space (Zustandsraum)
```
State = {
    grid: 10x10 Matrix,
    snake_head: (x, y),
    snake_body: [(x1, y1), (x2, y2), ...],
    food_position: (fx, fy),
    direction: Direction,
    distance_to_food: float,
    steps_since_food: int
}
```

### Action Space (Aktionsraum)
- **Typ**: Diskret
- **Größe**: 3 Aktionen
- **Encoding**: Integer [0, 1, 2]

### Reward Function (Belohnungsfunktion)

#### 🟢 Positive Rewards
- **+15**: Futter erfolgreich gesammelt
- **+2**: Annäherung an Futter (Distanzverringerung)
- **+100**: Spiel gewonnen (komplettes Feld gefüllt)

#### 🔴 Negative Rewards  
- **-15**: Game Over (Kollision mit Wand/Körper)
- **-1**: Entfernung vom Futter
- **-0.1**: Jeder Zeitschritt (Effizienz-Anreiz)
- **-5**: Zu lange ohne Futter (Anti-Loop-Mechanismus)

#### 🎯 Reward Engineering Prinzipien
- **Dense Rewards**: Kontinuierliches Feedback durch Distanz-basierte Belohnungen
- **Shaped Rewards**: Führung des Agents in Richtung optimaler Strategien
- **Penalty System**: Vermeidung von ineffizienten Verhaltensweisen

## 🔧 Technische Implementierung

### System Requirements
```python
Python >= 3.8
numpy >= 1.21.0
```

### Performance Optimierungen
- **Kompakte Spielfeldgröße**: 10x10 für reduzierte Komplexität
- **CLI-Only**: Keine GUI-Overhead
- **Vektorisierte Operationen**: NumPy für effiziente Berechnungen
- **Memory Efficient**: Minimaler Memory Footprint

### Environment Interface
```python
class SnakeGameRL:
    def reset() -> state
    def step(action: int) -> (state, reward, done, info)
    def render() -> None
    def get_state() -> np.ndarray
    def is_valid_action(action: int) -> bool
```

## 📊 Training Charakteristika

### Episode Eigenschaften
- **Durchschnittliche Länge**: 50-200 Steps
- **Maximale Länge**: 400 Steps (Anti-Loop-Limit)
- **Erfolgsrate**: Ziel >80% bei trainierten Agents
- **Konvergenz**: Typisch nach 10.000-50.000 Episoden

### Learning Challenges
1. **Exploration vs Exploitation**: Balance zwischen sicherer und optimaler Strategie
2. **Temporal Credit Assignment**: Langfristige Planung vs. sofortige Belohnungen
3. **Spatial Reasoning**: Navigation in begrenztem Raum
4. **Self-Avoidance**: Vermeidung von Selbst-Kollisionen bei wachsender Länge

## 🤖 Kompatible RL Algorithmen

### Empfohlene Algorithmen
- **Deep Q-Network (DQN)**: Ideal für diskrete Action Spaces
- **Double DQN**: Verbesserte Stabilität
- **Dueling DQN**: Bessere Value Function Approximation
- **Rainbow DQN**: State-of-the-Art DQN Variante

### Alternative Ansätze
- **Proximal Policy Optimization (PPO)**: Policy-based Methoden
- **Advantage Actor-Critic (A2C)**: Actor-Critic Architektur
- **Soft Actor-Critic (SAC)**: Für kontinuierliche Adaptionen

## 📈 Evaluation Metriken

### Performance Indikatoren
| Metrik | Beschreibung | Zielwert |
|--------|--------------|----------|
| Average Score | Durchschnittlicher Score pro Episode | >5.0 |
| Episode Length | Durchschnittliche Steps pro Episode | >100 |
| Success Rate | Anteil Episoden ohne Game Over | >0.8 |
| Food Efficiency | Steps pro gesammeltem Futter | <20 |
| Learning Stability | Varianz über letzte 100 Episoden | <2.0 |

### Benchmarking
- **Random Agent**: Baseline (~1.2 Score)
- **Greedy Agent**: Einfache Heuristik (~3.5 Score)  
- **Optimal Agent**: Theoretisches Maximum (~8.5 Score)

## 🗂️ Projektstruktur

```
snake-rl-project/
│
├── src/
│   ├── environment/
│   │   ├── snake_game.py      # Core Game Logic
│   │   ├── rewards.py         # Reward System
│   │   └── utils.py           # Helper Functions
│   │
│   ├── agents/
│   │   ├── dqn_agent.py       # DQN Implementation
│   │   ├── random_agent.py    # Baseline Agent
│   │   └── base_agent.py      # Agent Interface
│   │
│   ├── training/
│   │   ├── trainer.py         # Training Loop
│   │   ├── hyperparameters.py # Config Management
│   │   └── callbacks.py       # Training Callbacks
│   │
│   └── evaluation/
│       ├── evaluator.py       # Performance Testing
│       ├── metrics.py         # Metric Calculations
│       └── visualization.py   # Results Plotting
│
├── tests/
├── configs/
├── logs/
├── models/
├── requirements.txt
├── README.md
└── agent.md                   # Diese Dokumentation
```

## 🚀 Development Roadmap

### Phase 1: Foundation (Woche 1-2)
- [ ] Snake Game Environment Implementation
- [ ] Basic CLI Rendering System
- [ ] Reward System Integration
- [ ] Unit Tests für Core Logic

### Phase 2: Agent Development (Woche 3-4)
- [ ] DQN Agent Implementation
- [ ] Training Pipeline Setup
- [ ] Hyperparameter Configuration
- [ ] Logging und Monitoring

### Phase 3: Optimization (Woche 5-6)
- [ ] Performance Tuning
- [ ] Advanced RL Algorithmen
- [ ] Extensive Evaluation
- [ ] Documentation Completion

### Phase 4: Enhancement (Woche 7-8)
- [ ] Multi-Agent Experimente
- [ ] Curriculum Learning
- [ ] Advanced Metrics
- [ ] Research Paper Draft

## 🎓 Erwartete Lernerfahrungen

### Technische Skills
- Reinforcement Learning Environment Design
- Reward Engineering und Shaping
- Deep Learning für RL Applications
- Performance Optimization für Training

### Konzeptuelle Erkenntnisse
- Trade-offs zwischen Exploration und Exploitation
- Importance of Dense vs Sparse Rewards
- Stability Challenges in RL Training
- Evaluation Methodologies für RL Agents

## 📚 Referenzen und Inspiration

### Klassische RL Environments
- **OpenAI Gym**: Standard RL Environment Interface
- **Atari Games**: Klassische RL Benchmarks
- **GridWorld**: Einfache Navigation Tasks

### Snake Game RL Implementations
- Verschiedene GitHub Repositories als Referenz
- Academic Papers über Game-based RL
- RL Community Best Practices

---

**Autor**: NK
**Datum**: Mai 2025  
**Version**: 1.0  
**Status**: In Development