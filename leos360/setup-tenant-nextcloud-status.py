#!/usr/bin/env python3

import os
import sys
import re
import json
import requests
import argparse
import urllib3
from time import sleep
from dotenv import load_dotenv

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Color codes for output
GREEN = '\033[0;32m'
RED = '\033[0;31m'
YELLOW = '\033[0;33m'
BLUE = '\033[0;34m'
PURPLE = '\033[0;35m'  # Added for stage highlight
NC = '\033[0m'  # No Color

def print_colored(color, message):
    """Print with color"""
    print(f"{color}{message}{NC}")

def load_portainer_config():
    """Load Portainer configuration from .env file"""
    global_env_path = "/mnt/storage/docker/.env"
    
    if not os.path.isfile(global_env_path):
        print_colored(RED, f"Error: .env file not found at {global_env_path}")
        sys.exit(1)
    
    load_dotenv(global_env_path)
    
    api_key = os.getenv("PORTAINER_API_KEY")
    host = os.getenv("PORTAINER_HOST")
    
    if not api_key or not host:
        print_colored(RED, "Error: PORTAINER_API_KEY or PORTAINER_HOST not defined")
        sys.exit(1)
    
    return {
        "api_key": api_key,
        "host": host,
        "endpoint_id": os.getenv("ENDPOINT_ID", "11"),
        "portainer_url": f"https://{host}:9443"
    }

def verify_nextcloud_setup(customer_name, config, timeout_minutes=15):
    """
    Verify that Nextcloud setup has completed by checking logs via Portainer API.
    Also monitors and reports the various stages of the setup process.
    
    Args:
        customer_name: Name of the customer tenant
        config: Dictionary with Portainer API configuration
        timeout_minutes: Maximum time to wait for setup completion
        
    Returns:
        bool: True if setup completed successfully, False otherwise
    """
    print_colored(BLUE, f"Verifying Nextcloud setup completion for {customer_name}...")
    
    # API headers
    headers = {
        "X-API-Key": config["api_key"],
        "Content-Type": "application/json"
    }
    
    # Calculate timeout
    timeout_seconds = timeout_minutes * 60
    check_interval = 30  # Check every 30 seconds
    elapsed_time = 0
    
    # Define setup stages and their patterns
    setup_stages = [
        {
            "id": 1,
            "name": "Initializing Nextcloud",
            "patterns": [r'Initializing nextcloud \d+\.\d+\.\d+', r'New nextcloud instance'],
            "detected": False
        },
        {
            "id": 2,
            "name": "Install DB",
            "patterns": [r'Installing with PostgreSQL database'],
            "detected": False
        },
        {
            "id": 3,
            "name": "Start Grundsetup Nextcloud",
            "patterns": [r'Starting nextcloud installation'],
            "detected": False
        },
        {
            "id": 4,
            "name": "Start leos360-setup Nextcloud",
            "patterns": [r'Running the script .* /docker-entrypoint-hooks.d/post-installation/50-setup-nc.sh'],
            "detected": False
        },
        {
            "id": 5,
            "name": "FINISH",
            "patterns": [
                r'Nextcloud Setup abgeschlossen',
                r'Finished executing the script: "/docker-entrypoint-hooks.d/post-installation/50-setup-nc.sh"',
                r'Completed executing scripts in the "post-installation" folder',
                r'Initializing finished'
            ],
            "detected": False
        }
    ]
    
    # Success patterns to look for in logs
    success_patterns = [
        'Nextcloud Setup abgeschlossen',
        'Initializing finished',
        'Completed executing scripts in the "post-installation" folder'
    ]
    
    container_name = f"{customer_name}-nextcloud"
    endpoint_id = config["endpoint_id"]
    portainer_url = config["portainer_url"]
    container_id = None
    current_stage = 0
    
    while elapsed_time < timeout_seconds:
        try:
            # Step 1: Get container list to find the Nextcloud container
            containers_url = f"{portainer_url}/api/endpoints/{endpoint_id}/docker/containers/json?all=true"
            
            print_colored(YELLOW, f"Checking for container {container_name}...")
            
            response = requests.get(
                containers_url,
                headers=headers,
                verify=False
            )
            
            if response.status_code != 200:
                print_colored(RED, f"Error getting container list: {response.status_code}")
                print_colored(RED, f"Response: {response.text}")
                sleep(check_interval)
                elapsed_time += check_interval
                continue
            
            # Find the Nextcloud container
            container_id = None
            container_status = None
            
            for container in response.json():
                for name in container.get("Names", []):
                    clean_name = name[1:] if name.startswith("/") else name
                    if clean_name == container_name:
                        container_id = container.get("Id")
                        container_status = container.get("State")
                        break
                if container_id:
                    break
            
            if not container_id:
                print_colored(YELLOW, f"Container {container_name} not found. Waiting...")
                sleep(check_interval)
                elapsed_time += check_interval
                continue
            
            print_colored(BLUE, f"Found container {container_name}, status: {container_status}")
            
            # Check if container is running
            if container_status != "running":
                print_colored(YELLOW, f"Container {container_name} is not running (status: {container_status}). Waiting...")
                sleep(check_interval)
                elapsed_time += check_interval
                continue
            
            # Step 2: Get container logs
            logs_url = f"{portainer_url}/api/endpoints/{endpoint_id}/docker/containers/{container_id}/logs?stderr=1&stdout=1&tail=1000"
            
            response = requests.get(
                logs_url,
                headers=headers,
                verify=False
            )
            
            if response.status_code != 200:
                print_colored(RED, f"Error getting container logs: {response.status_code}")
                print_colored(RED, f"Response: {response.text}")
                sleep(check_interval)
                elapsed_time += check_interval
                continue
            
            # Decode logs - they're in a binary format with headers
            logs = ""
            try:
                for line in response.content.split(b'\n'):
                    if len(line) > 8:  # Skip empty lines or header-only lines
                        # Skip the first 8 bytes (Docker log header)
                        logs += line[8:].decode('utf-8', errors='replace') + "\n"
            except Exception as e:
                print_colored(YELLOW, f"Warning: Error decoding logs: {str(e)}")
            
            # Step 3: Check for setup stages in logs
            highest_stage_detected = current_stage
            
            for stage in setup_stages:
                if stage["detected"]:
                    continue  # Skip already detected stages
                
                stage_detected = False
                for pattern in stage["patterns"]:
                    if re.search(pattern, logs, re.IGNORECASE):
                        stage_detected = True
                        stage["detected"] = True
                        if stage["id"] > highest_stage_detected:
                            highest_stage_detected = stage["id"]
                        break
            
            # Report new stages
            if highest_stage_detected > current_stage:
                for stage_id in range(current_stage + 1, highest_stage_detected + 1):
                    for stage in setup_stages:
                        if stage["id"] == stage_id and stage["detected"]:
                            print_colored(PURPLE, f"[STAGE {stage['id']}] ➡️ {stage['name']} detected after {elapsed_time//60}m {elapsed_time%60}s")
                
                current_stage = highest_stage_detected
                
                # Check if we reached the final stage
                if current_stage == 5:
                    print_colored(GREEN, f"✅ Final stage reached! Nextcloud setup completed successfully!")
                    return True
            
            # Step 4: Check for success patterns in logs (legacy check)
            success_found = False
            for pattern in success_patterns:
                if re.search(pattern, logs, re.IGNORECASE):
                    success_found = True
                    break
            
            if success_found:
                return True
            
            # Step 5: Try to check HTTP status (using an exec in the container to make a curl request)
            try:
                # Create exec instance for curl command
                exec_url = f"{portainer_url}/api/endpoints/{endpoint_id}/docker/containers/{container_id}/exec"
                
                exec_payload = {
                    "AttachStdin": False,
                    "AttachStdout": True,
                    "AttachStderr": True,
                    "Tty": False,
                    "Cmd": ["curl", "-s", "-o", "/dev/null", "-w", "%{http_code}", "http://localhost/status.php"]
                }
                
                exec_response = requests.post(
                    exec_url,
                    headers=headers,
                    json=exec_payload,
                    verify=False
                )
                
                if exec_response.status_code == 201:
                    exec_id = exec_response.json().get("Id")
                    
                    # Start the exec instance
                    start_url = f"{portainer_url}/api/endpoints/{endpoint_id}/docker/exec/{exec_id}/start"
                    start_payload = {"Detach": False, "Tty": False}
                    
                    start_response = requests.post(
                        start_url,
                        headers=headers,
                        json=start_payload,
                        verify=False
                    )
                    
                    if start_response.status_code == 200:
                        # Try to extract HTTP status code from response
                        content = start_response.content.decode('utf-8', errors='replace')
                        status_code = content.strip()
                        
                        if status_code in ["200", "301", "302"]:
                            print_colored(GREEN, f"✅ Status endpoint is responding with code {status_code}")
                            return True
                        else:
                            print_colored(YELLOW, f"Status endpoint returned code: {status_code}")
            except Exception as e:
                # Ignore curl check errors
                print_colored(YELLOW, f"Warning: Error checking status endpoint: {str(e)}")
            
            # Show setup progress summary (commented out)
            # print_colored(BLUE, f"Current progress: Stage {current_stage} of 5")
            # for stage in setup_stages:
            #     status = "✅" if stage["detected"] else "⏳"
            #     print_colored(BLUE if stage["detected"] else YELLOW, f"  {status} Stage {stage['id']}: {stage['name']}")
            
            # If we're still here, show the last few log lines (commented out)
            # print_colored(BLUE, "Recent logs:")
            # for line in log_lines:
            #     print(f"  {line}")
            
            print_colored(YELLOW, f"Waiting for Nextcloud setup...")
            sleep(check_interval)
            elapsed_time += check_interval
            
        except Exception as e:
            print_colored(RED, f"Warning: Error during verification: {str(e)}")
            sleep(check_interval)
            elapsed_time += check_interval
    
    # Timeout reached
    print_colored(RED, f"❌ Warning: Timed out after {timeout_minutes} minutes waiting for Nextcloud setup to complete.")
    
    # Show final debug info
    try:
        if container_id:
            # Get container details
            detail_url = f"{portainer_url}/api/endpoints/{endpoint_id}/docker/containers/{container_id}/json"
            detail_response = requests.get(detail_url, headers=headers, verify=False)
            
            if detail_response.status_code == 200:
                container_details = detail_response.json()
                print_colored(BLUE, f"Container state:")
                state = container_details.get('State', {})
                for key, value in state.items():
                    print(f"  {key}: {value}")
            
            # Get final logs
            logs_url = f"{portainer_url}/api/endpoints/{endpoint_id}/docker/containers/{container_id}/logs?stderr=1&stdout=1&tail=20"
            logs_response = requests.get(logs_url, headers=headers, verify=False)
            
            if logs_response.status_code == 200:
                # Decode logs
                final_logs = ""
                for line in logs_response.content.split(b'\n'):
                    if len(line) > 8:
                        final_logs += line[8:].decode('utf-8', errors='replace') + "\n"
                
                print_colored(BLUE, "Last log lines:")
                # for line in final_logs.strip().split('\n'):
                #     print(f"  {line}")
        
    except Exception as e:
        print_colored(RED, f"Could not retrieve final debug info: {str(e)}")
    
    return False

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Verify Nextcloud setup completion')
    parser.add_argument('customer_name', help='Name of the customer tenant')
    parser.add_argument('--timeout', type=int, default=15, help='Maximum wait time in minutes (default: 15)')
    
    args = parser.parse_args()
    
    # Validate customer name (lowercase letters, numbers, hyphens only)
    if not re.match(r'^[a-z0-9-]+$', args.customer_name):
        print_colored(RED, "Error: Customer name must contain only lowercase letters, numbers and hyphens")
        sys.exit(1)
    
    # Load Portainer configuration
    config = load_portainer_config()
    
    print_colored(GREEN, f"Verifying Nextcloud setup for customer: {args.customer_name}")
    print_colored(YELLOW, f"Using Portainer at: {config['portainer_url']}")
    print_colored(YELLOW, f"With endpoint ID: {config['endpoint_id']}")
    print_colored(YELLOW, f"Timeout set to: {args.timeout} minutes")
    print("----------------------------------------")
    
    # Verify Nextcloud setup
    result = verify_nextcloud_setup(args.customer_name, config, args.timeout)
    
    if result:
        print_colored(GREEN, f"✅ Nextcloud setup verification successful for {args.customer_name}")
        print_colored(GREEN, f"Nextcloud URL: https://{args.customer_name}.leos360.cloud")
        sys.exit(0)
    else:
        print_colored(RED, f"❌ Nextcloud setup verification failed or timed out for {args.customer_name}")
        print_colored(YELLOW, "The installation might still be in progress.")
        print_colored(YELLOW, f"You can check the status manually at: https://{args.customer_name}.leos360.cloud")
        sys.exit(1)

if __name__ == "__main__":
    main()