# Enhanced Tenant Setup - Konfiguration & Installation

## Konfigurationsdatei (tenant_setup.yaml)

```yaml
# Enhanced Tenant Setup Configuration
# Speichere diese Datei als tenant_setup.yaml im gleichen Verzeichnis wie das Script

# Netzwerk-Konfiguration
networking:
  ip_range:
    start: 20           # Erste IP: ***********
    end: 150           # Letzte IP: ************
    prefix: "172.16.7." # IP-Prefix

# Datenbank-Konfiguration
database:
  host: "************"
  port: "5432"

# Passwort-Richtlinien
password_policy:
  length: 32                # Passwort-Länge
  exclude_chars: "=+/"     # Ausgeschlossene Zeichen

# Pfad-Konfiguration
paths:
  master_base: "/mnt/storage/setup"
  tenants_base: "/mnt/storage/tenants"

# Domain-Konfiguration
domain:
  base_domain: "leos360.cloud"

# Logging-Konfiguration
logging:
  level: "INFO"            # DEBUG, INFO, WARNING, ERROR
  log_directory: "/var/log/tenant_setup"
  keep_logs_days: 30

# Performance-Optimierungen
performance:
  parallel_workers: 4      # Anzahl paralleler Workers
  ip_cache_timeout: 300    # IP-Cache Timeout in Sekunden
  disk_space_check: true   # Speicherplatz-Prüfung aktivieren
  required_space_gb: 1.0   # Minimum freier Speicherplatz

# Sicherheits-Einstellungen
security:
  file_permissions:
    secrets: 0o600         # Nur Owner
    configs: 0o640         # Owner + Group read
    scripts: 0o750         # Owner + Group execute
    directories: 0o750     # Directory permissions
    regular: 0o644         # Regular files

  # Reservierte Kundennamen (zusätzlich zu den Standardwerten)
  reserved_names:
    - "backup"
    - "monitoring"
    - "logs"

# Service-spezifische Konfiguration
services:
  redis:
    max_databases: 16      # Maximum Redis Databases
  
  ssl:
    verify_certificates: false  # SSL-Zertifikate validieren
  
  database:
    connection_timeout: 30  # Verbindungs-Timeout in Sekunden
```

## Installation und Dependencies

### 1. Python Dependencies installieren

```bash
# System-Pakete (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install -y python3.12 python3-pip python3-yaml

# Python-Pakete installieren
pip3 install --user pyyaml tqdm

# Oder mit requirements.txt:
echo "PyYAML>=6.0
tqdm>=4.64.0" > requirements.txt

pip3 install -r requirements.txt
```

### 2. Verzeichnisstruktur vorbereiten

```bash
# Hauptverzeichnisse erstellen
sudo mkdir -p /mnt/storage/setup
sudo mkdir -p /mnt/storage/tenants
sudo mkdir -p /var/log/tenant_setup

# Berechtigungen setzen
sudo chown root:admin /mnt/storage/setup
sudo chown root:admin /mnt/storage/tenants
sudo chown root:admin /var/log/tenant_setup
sudo chmod 755 /mnt/storage/setup
sudo chmod 755 /mnt/storage/tenants
sudo chmod 755 /var/log/tenant_setup
```

### 3. Script installieren

```bash
# Script ausführbar machen
chmod +x enhanced_tenant_setup.py

# Optional: In PATH installieren
sudo cp enhanced_tenant_setup.py /usr/local/bin/tenant-setup
sudo chmod +x /usr/local/bin/tenant-setup

# Konfigurationsdatei kopieren
cp tenant_setup.yaml /etc/tenant_setup.yaml
# oder in Home-Verzeichnis: ~/.config/tenant_setup.yaml
```

## Verwendung

### Basis-Kommandos

```bash
# Normaler Setup-Lauf
sudo python3 enhanced_tenant_setup.py acme-corp

# Dry-Run (Test ohne Änderungen)
sudo python3 enhanced_tenant_setup.py acme-corp --dry-run

# Mit custom Config
sudo python3 enhanced_tenant_setup.py acme-corp --config /path/to/config.yaml

# Verbose Logging
sudo python3 enhanced_tenant_setup.py acme-corp --log-level DEBUG

# Kombiniert
sudo python3 enhanced_tenant_setup.py acme-corp --dry-run --log-level DEBUG --config custom.yaml
```

### Mit installiertem Script

```bash
# Nach Installation in /usr/local/bin
sudo tenant-setup acme-corp
sudo tenant-setup acme-corp --dry-run
```

## Verbesserungen gegenüber Original-Script

### 🚀 Performance-Optimierungen

1. **Parallele Service-Einrichtung** - Services werden parallel statt sequenziell eingerichtet
2. **IP-Caching** - Verwendete IPs werden gecacht (5 Min) für schnellere Allokation
3. **Optimierte File-Operations** - Bessere I/O-Performance
4. **Progress-Tracking** - Fortschrittsanzeige mit tqdm

### 🛡️ Robustheit & Fehlerbehandlung

1. **Rollback-Mechanismus** - Automatisches Cleanup bei Fehlern
2. **Template-Validierung** - Prüfung der Template-Integrität
3. **Enhanced Exception Handling** - Bessere Fehlerbehandlung
4. **Health Checks** - Umfassende Validierung nach Setup

### ⚙️ Konfiguration & Flexibilität

1. **Externe Konfiguration** - YAML-basierte Konfiguration
2. **Dry-Run Modus** - Testen ohne Änderungen
3. **Konfigurierbare Parameter** - Alle wichtigen Werte konfigurierbar
4. **Environment-specific Settings** - Anpassbar für verschiedene Umgebungen

### 📊 Monitoring & Logging

1. **Strukturiertes Logging** - JSON-basierte Logs mit Timestamps
2. **Performance Metrics** - Zeitmessung für jeden Schritt
3. **File Logging** - Persistente Logs für Audit-Zwecke
4. **Log Rotation** - Konfigurierbare Log-Aufbewahrung

### 🔒 Sicherheitsverbesserungen

1. **Erweiterte Input-Validierung** - Comprehensive Prüfung der Eingaben
2. **Granulare Berechtigungen** - Konfigurierbare File-Permissions
3. **Sichere Passwort-Generierung** - Verbesserte Entropie
4. **Reserved Names Check** - Schutz vor reservierten Namen

## Monitoring und Wartung

### Log-Dateien prüfen

```bash
# Letzte Setup-Logs anzeigen
ls -la /var/log/tenant_setup/

# Spezifisches Log anzeigen
tail -f /var/log/tenant_setup/acme-corp_20250522_143022.log

# Alle Fehler finden
grep ERROR /var/log/tenant_setup/*.log
```

### Health Check ausführen

```bash
# Health Check für bestehende Tenants
python3 -c "
from enhanced_tenant_setup import EnhancedTenantConfigSetup, Config
setup = EnhancedTenantConfigSetup('acme-corp', Config())
print(setup.health_check())
"
```

### Performance Monitoring

Das Script loggt Performance-Metriken für jeden Schritt:

```
=== Performance Summary ===
Prerequisites Check: 0.12s
IP Allocation: 0.03s
Password Generation: 0.05s
Directory Creation: 0.08s
Environment File: 0.02s
Services Setup: 2.34s
Docker Compose: 0.01s
Setup Verification: 0.15s
Total execution time: 2.80s
```

## Troubleshooting

### Häufige Probleme

1. **Permission Denied**
   ```bash
   # Script muss als root ausgeführt werden
   sudo python3 enhanced_tenant_setup.py customer-name
   ```

2. **Missing Dependencies**
   ```bash
   # Alle Dependencies installieren
   pip3 install PyYAML tqdm
   ```

3. **Template Fehler**
   ```bash
   # Templates validieren
   python3 enhanced_tenant_setup.py customer-name --dry-run --log-level DEBUG
   ```

4. **IP-Range erschöpft**
   - Konfigurationsdatei anpassen: `ip_range.end` erhöhen
   - Oder alte Tenants aufräumen

### Debug-Modus

```bash
# Maximale Verbosity für Debugging
sudo python3 enhanced_tenant_setup.py customer-name --log-level DEBUG --dry-run
```

## Migration vom Original-Script

Das Enhanced Script ist vollständig rückwärts-kompatibel:

1. Gleiche Template-Struktur
2. Gleiche Verzeichnis-Layouts  
3. Gleiche Umgebungsvariablen
4. Gleiche Ausgabe-Formate

**Migration Steps:**
1. Dependencies installieren
2. Konfigurationsdatei erstellen (optional)
3. Original-Script durch Enhanced Version ersetzen
4. Testen mit `--dry-run`
5. Normalen Betrieb aufnehmen

Die Enhanced Version kann parallel zum Original laufen und die gleichen Templates verwenden.