#!/usr/bin/env python3
import sys
import argparse
import requests

def get_customer_ip(customer_name):
    """Get customer IP from .env file"""
    env_path = f"/mnt/storage/tenants/{customer_name}/.env"
    try:
        with open(env_path, 'r') as f:
            for line in f:
                if line.startswith('CUSTOMER_IP='):
                    return line.strip().split('=', 1)[1]
        print(f"Error: CUSTOMER_IP not found in {env_path}")
        raise ValueError(f"CUSTOMER_IP not found in {env_path}")
    except Exception as e:
        print(f"Error: Could not read CUSTOMER_IP from {env_path}")
        print(f"Detail: {str(e)}")
        raise

def setup_webproxy(customer_name, customer_ip):
    """Setup web proxy via API call"""
    api_url = "http://************:5000/webproxy/add/" + customer_name
    payload = {"ip": customer_ip}
    
    try:
        response = requests.post(api_url, json=payload, timeout=10)
        
        if response.status_code != 200:
            print(f"Error: Failed to setup web proxy for {customer_name}")
            print(f"API response: {response.text}")
            raise Exception(f"Failed to setup web proxy for {customer_name}")
            
        print(f"Web proxy setup successful for {customer_name} with IP {customer_ip}")
        return response.json()
        
    except requests.exceptions.RequestException as e:
        print(f"Error: Failed to connect to web proxy API")
        print(f"Detail: {str(e)}")
        raise

def main():
    parser = argparse.ArgumentParser(description='Setup web proxy for a tenant')
    parser.add_argument('customer_name', help='Name of the customer')
    parser.add_argument('--ip', help='IP address of the customer (optional, will be read from .env if not provided)')
    
    args = parser.parse_args()
    
    try:
        # Use provided IP if available, otherwise read from .env
        customer_ip = args.ip if args.ip else get_customer_ip(args.customer_name)
        result = setup_webproxy(args.customer_name, customer_ip)
        sys.exit(0)
    except Exception as e:
        print(f"Error setting up web proxy: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()