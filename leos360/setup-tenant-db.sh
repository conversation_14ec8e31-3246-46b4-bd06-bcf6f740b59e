#!/usr/bin/env bash
# Kundenname als Parameter nehmen
if [ -n "$1" ]; then
  CUSTOMER_NAME="$1"
else
  echo "Fehler: Bitte Kundennamen angeben"
  echo "Beispiel: $0 testkunde5"
  exit 1
fi

# Zweiter Parameter für die Operation
OPERATION="setup"
if [ "$2" = "--reset" ]; then
  OPERATION="reset"
elif [ "$2" = "--status" ]; then
  OPERATION="status"
fi

# Pfade definieren
SQL_FILE="/mnt/storage/tenants/${CUSTOMER_NAME}/db/db_setup_${CUSTOMER_NAME}.sql"
ENV_FILE="/mnt/storage/tenants/${CUSTOMER_NAME}/.env"

# Prüfen, ob SQL-Datei existiert (nur für setup notwendig)
if [ "$OPERATION" = "setup" ] && [ ! -f "$SQL_FILE" ]; then
  echo "Fehler: SQL-Datei nicht gefunden: ${SQL_FILE}"
  exit 1
fi

# .env-Datei einlesen für Passwort
if [ -f "$ENV_FILE" ]; then
  echo "Lese .env-Datei: ${ENV_FILE}"
  
  # Extrahiere nur die DB_ADMIN_PASSWORD Variable direkt
  DB_ADMIN_PASSWORD=$(grep -E "^DB_ADMIN_PASSWORD=" "$ENV_FILE" | cut -d '=' -f2- | tr -d '"' | tr -d "'")
  
  if [ -z "$DB_ADMIN_PASSWORD" ]; then
    echo "Fehler: DB_ADMIN_PASSWORD nicht in .env-Datei gefunden"
    exit 1
  fi
else
  echo "Fehler: .env-Datei nicht gefunden: ${ENV_FILE}"
  exit 1
fi

# Temporäre PGPASS-Datei erstellen
PGPASS_FILE=$(mktemp)
echo "172.16.8.110:5432:*:postgres:${DB_ADMIN_PASSWORD}" > "$PGPASS_FILE"
chmod 600 "$PGPASS_FILE"

# Cleanup on any exit
trap 'rm -f "$PGPASS_FILE"' EXIT

# Locale auf C setzen
export LC_ALL=C
export LANG=C
export PGPASSFILE="$PGPASS_FILE"
export PGPASSWORD="${DB_ADMIN_PASSWORD}"

# Check if databases already exist
echo "Prüfe, ob Datenbanken bereits existieren..."
check_command="SELECT COUNT(*) FROM pg_database WHERE datname IN ('${CUSTOMER_NAME}_nextcloud', '${CUSTOMER_NAME}_keycloak', '${CUSTOMER_NAME}_lldap');"
db_count=$(psql -U postgres -h 172.16.8.110 -t -c "$check_command" | tr -d ' ')

# Check if user already exists
user_command="SELECT COUNT(*) FROM pg_roles WHERE rolname = '${CUSTOMER_NAME}_admin';"
user_exists=$(psql -U postgres -h 172.16.8.110 -t -c "$user_command" | tr -d ' ')

# Operation: Status
if [ "$OPERATION" = "status" ]; then
  echo "Status für Kunde ${CUSTOMER_NAME}:"
  
  if [ "$user_exists" -gt "0" ]; then
    echo "- Benutzer ${CUSTOMER_NAME}_admin existiert"
  else
    echo "- Benutzer ${CUSTOMER_NAME}_admin existiert NICHT"
  fi
  
  if [ "$db_count" -gt "0" ]; then
    echo "- Datenbanken:"
    list_db="SELECT datname FROM pg_database WHERE datname IN ('${CUSTOMER_NAME}_nextcloud', '${CUSTOMER_NAME}_keycloak', '${CUSTOMER_NAME}_lldap');"
    psql -U postgres -h 172.16.8.110 -t -c "$list_db" | tr -d ' ' | sed '/^$/d' | sed 's/^/  - /'
  else
    echo "- Keine Datenbanken gefunden"
  fi
  
  exit 0
fi

# Operation: Reset
if [ "$OPERATION" = "reset" ]; then
  if [ "$db_count" -eq "0" ] && [ "$user_exists" -eq "0" ]; then
    echo "Keine Objekte zum Löschen vorhanden."
    exit 0
  fi
  
  echo "Folgende Objekte werden gelöscht:"
  
  if [ "$user_exists" -gt "0" ]; then
    echo "- Benutzer ${CUSTOMER_NAME}_admin"
  fi
  
  if [ "$db_count" -gt "0" ]; then
    echo "- Datenbanken:"
    list_db="SELECT datname FROM pg_database WHERE datname IN ('${CUSTOMER_NAME}_nextcloud', '${CUSTOMER_NAME}_keycloak', '${CUSTOMER_NAME}_lldap');"
    psql -U postgres -h 172.16.8.110 -t -c "$list_db" | tr -d ' ' | sed '/^$/d' | sed 's/^/  - /'
  fi
  
  # Create reset SQL
  echo "Lösche bestehende Objekte..."
  RESET_SQL=$(mktemp)
  
  # Create SQL commands to terminate connections and drop databases
  cat > "$RESET_SQL" <<EOF
-- Terminate connections
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE datname IN ('${CUSTOMER_NAME}_nextcloud', '${CUSTOMER_NAME}_keycloak', '${CUSTOMER_NAME}_lldap');

-- Drop databases
DROP DATABASE IF EXISTS ${CUSTOMER_NAME}_nextcloud;
DROP DATABASE IF EXISTS ${CUSTOMER_NAME}_keycloak;
DROP DATABASE IF EXISTS ${CUSTOMER_NAME}_lldap;

-- Drop user (this must come after dropping the databases)
DROP USER IF EXISTS ${CUSTOMER_NAME}_admin;
EOF
  
  # Execute reset SQL
  psql -U postgres -h 172.16.8.110 -f "$RESET_SQL" --no-psqlrc
  rm -f "$RESET_SQL"
  
  echo "Löschvorgang abgeschlossen."
  exit 0
fi

# Operation: Setup
if [ "$OPERATION" = "setup" ]; then
  # Check if objects already exist
  if [ "$db_count" -gt "0" ] || [ "$user_exists" -gt "0" ]; then
    echo "Setup kann nicht durchgeführt werden, da bereits Objekte existieren:"
    
    if [ "$user_exists" -gt "0" ]; then
      echo "- Benutzer ${CUSTOMER_NAME}_admin existiert bereits"
    fi
    
    if [ "$db_count" -gt "0" ]; then
      echo "- Es existieren bereits Datenbanken für ${CUSTOMER_NAME}"
      # List existing databases
      echo "Bestehende Datenbanken:"
      list_db="SELECT datname FROM pg_database WHERE datname IN ('${CUSTOMER_NAME}_nextcloud', '${CUSTOMER_NAME}_keycloak', '${CUSTOMER_NAME}_lldap');"
      psql -U postgres -h 172.16.8.110 -t -c "$list_db" | tr -d ' ' | sed '/^$/d' | sed 's/^/- /'
    fi
    
    echo "Verwenden Sie zuerst --reset, um bestehende Objekte zu löschen und dann führen Sie das Setup erneut aus."
    exit 1
  fi
  
  # Proceed with setup
  echo "Führe SQL-Datei aus: ${SQL_FILE}"
  psql -U postgres -h 172.16.8.110 -f "$SQL_FILE" --no-psqlrc
  RESULT=$?
  
  if [ "$RESULT" -eq 0 ]; then
    echo "DB Setup erfolgreich abgeschlossen."
  else
    echo "Fehler beim DB Setup. Exit-Code: $RESULT"
  fi
  
  exit $RESULT
fi