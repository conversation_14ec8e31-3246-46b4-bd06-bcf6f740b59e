#!/usr/bin/env python3

import os
import sys
import json
import requests
import time
import yaml
import argparse
import logging
import concurrent.futures
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from pathlib import Path
from dotenv import load_dotenv
from tqdm import tqdm
import datetime
import urllib3

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Color codes for output
class Colors:
    GREEN = '\033[0;32m'
    RED = '\033[0;31m'
    YELLOW = '\033[0;33m'
    BLUE = '\033[0;34m'
    CYAN = '\033[0;36m'
    MAGENTA = '\033[0;35m'
    WHITE = '\033[0;37m'
    BOLD = '\033[1m'
    NC = '\033[0m'  # No Color


@dataclass
class DeploymentConfig:
    """Configuration for Portainer deployment"""
    # Portainer connection
    portainer_host: str = ""
    portainer_api_key: str = ""
    endpoint_id: str = "11"
    portainer_url: str = ""
    
    # Paths
    tenants_base: Path = Path("/mnt/storage/tenants")
    global_env_path: Path = Path("/mnt/storage/docker/.env")
    
    # Deployment settings
    stack_prefix: str = "leos360"
    prune_on_update: bool = True
    deployment_timeout: int = 300  # 5 minutes
    retry_attempts: int = 3
    retry_delay: int = 5  # seconds
    
    # Performance settings
    parallel_deployments: int = 3
    api_timeout: int = 30
    
    # Health check settings
    health_check_enabled: bool = True
    health_check_timeout: int = 120
    health_check_interval: int = 10
    
    # Validation settings
    validate_compose_files: bool = True
    required_services: List[str] = field(default_factory=list)
    
    @classmethod
    def from_file(cls, config_path: Path) -> 'DeploymentConfig':
        """Load configuration from YAML file"""
        if not config_path.exists():
            return cls()
        
        with open(config_path) as f:
            data = yaml.safe_load(f)
        
        config = cls()
        
        # Update configuration from file
        for key, value in data.items():
            if hasattr(config, key):
                if isinstance(getattr(config, key), Path):
                    setattr(config, key, Path(value))
                else:
                    setattr(config, key, value)
        
        return config


class DeploymentLogger:
    """Enhanced logging for deployment operations"""
    
    def __init__(self, log_level: str = "INFO", log_file: Optional[Path] = None):
        self.logger = logging.getLogger("portainer_deployment")
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Setup handlers
        self._setup_handlers(log_file)
        
        # Performance tracking
        self.start_time = time.time()
        self.operation_times = {}
    
    def _setup_handlers(self, log_file: Optional[Path] = None):
        """Setup logging handlers"""
        # Console handler with colors
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # File handler if specified
        if log_file:
            log_file.parent.mkdir(parents=True, exist_ok=True)
            file_handler = logging.FileHandler(log_file)
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
    
    def print_colored(self, color: str, message: str, level: str = "info"):
        """Print colored message and log it"""
        colored_message = f"{color}{message}{Colors.NC}"
        print(colored_message)
        
        # Also log to file
        if level.lower() == "error":
            self.logger.error(message)
        elif level.lower() == "warning":
            self.logger.warning(message)
        else:
            self.logger.info(message)
    
    def time_operation(self, operation_name: str):
        """Decorator to time operations"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                start = time.time()
                try:
                    result = func(*args, **kwargs)
                    duration = time.time() - start
                    self.operation_times[operation_name] = duration
                    self.logger.info(f"Operation {operation_name} completed in {duration:.2f}s")
                    return result
                except Exception as e:
                    duration = time.time() - start
                    self.logger.error(f"Operation {operation_name} failed after {duration:.2f}s: {e}")
                    raise
            return wrapper
        return decorator
    
    def log_performance_summary(self):
        """Log performance summary"""
        total_time = time.time() - self.start_time
        self.print_colored(Colors.CYAN, "\n=== Performance Summary ===")
        
        for operation, duration in self.operation_times.items():
            self.print_colored(Colors.WHITE, f"{operation}: {duration:.2f}s")
        
        self.print_colored(Colors.CYAN, f"Total execution time: {total_time:.2f}s")


class PortainerAPIClient:
    """Enhanced Portainer API client with retry logic and validation"""
    
    def __init__(self, config: DeploymentConfig, logger: DeploymentLogger):
        self.config = config
        self.logger = logger
        self.session = requests.Session()
        self.session.headers.update({
            "X-API-Key": config.portainer_api_key,
            "Content-Type": "application/json"
        })
        self.session.verify = False
    
    def _make_request(self, method: str, url: str, **kwargs) -> requests.Response:
        """Make HTTP request with retry logic"""
        for attempt in range(self.config.retry_attempts):
            try:
                kwargs.setdefault('timeout', self.config.api_timeout)
                response = self.session.request(method, url, **kwargs)
                
                # Log request details in debug mode
                self.logger.logger.debug(f"{method} {url} - Status: {response.status_code}")
                
                return response
                
            except requests.exceptions.RequestException as e:
                if attempt < self.config.retry_attempts - 1:
                    self.logger.print_colored(
                        Colors.YELLOW, 
                        f"Request failed (attempt {attempt + 1}/{self.config.retry_attempts}): {e}",
                        "warning"
                    )
                    time.sleep(self.config.retry_delay * (attempt + 1))  # Exponential backoff
                else:
                    raise
        
        raise Exception("All retry attempts failed")
    
    def get_stacks(self) -> List[Dict]:
        """Get all stacks from Portainer"""
        response = self._make_request("GET", f"{self.config.portainer_url}/api/stacks")
        
        if response.status_code != 200:
            raise Exception(f"Failed to get stacks: {response.status_code} - {response.text}")
        
        return response.json()
    
    def get_stack_by_name(self, stack_name: str) -> Optional[Dict]:
        """Get stack by name"""
        stacks = self.get_stacks()
        
        for stack in stacks:
            if stack.get("Name") == stack_name:
                return stack
        
        return None
    
    def create_stack(self, stack_name: str, compose_file: Path, env_vars: List[Dict]) -> bool:
        """Create new stack"""
        url = f"{self.config.portainer_url}/api/stacks/create/standalone/file?endpointId={self.config.endpoint_id}"
        
        files = {'file': (compose_file.name, open(compose_file, 'rb'))}
        data = {
            'Name': stack_name,
            'Env': json.dumps(env_vars)
        }
        
        # Use different headers for multipart request
        headers = {"X-API-Key": self.config.portainer_api_key}
        
        response = self._make_request("POST", url, headers=headers, files=files, data=data)
        
        files['file'][1].close()  # Close file handle
        
        if 200 <= response.status_code < 300:
            self.logger.print_colored(Colors.GREEN, f"Stack {stack_name} created successfully!")
            return True
        else:
            self.logger.print_colored(
                Colors.RED, 
                f"Failed to create stack: {response.status_code} - {response.text}",
                "error"
            )
            return False
    
    def update_stack(self, stack_id: int, compose_content: str, env_vars: List[Dict]) -> bool:
        """Update existing stack"""
        url = f"{self.config.portainer_url}/api/stacks/{stack_id}?endpointId={self.config.endpoint_id}"
        
        payload = {
            "stackFileContent": compose_content,
            "env": env_vars,
            "prune": self.config.prune_on_update
        }
        
        response = self._make_request("PUT", url, json=payload)
        
        if 200 <= response.status_code < 300:
            self.logger.print_colored(Colors.GREEN, f"Stack {stack_id} updated successfully!")
            return True
        else:
            self.logger.print_colored(
                Colors.RED, 
                f"Failed to update stack: {response.status_code} - {response.text}",
                "error"
            )
            return False
    
    def delete_stack(self, stack_id: int) -> bool:
        """Delete stack"""
        url = f"{self.config.portainer_url}/api/stacks/{stack_id}"
        
        response = self._make_request("DELETE", url)
        
        if response.status_code in [200, 204, 404]:
            self.logger.print_colored(Colors.GREEN, f"Stack {stack_id} deleted successfully!")
            return True
        else:
            self.logger.print_colored(
                Colors.RED, 
                f"Failed to delete stack: {response.status_code} - {response.text}",
                "error"
            )
            return False
    
    def get_stack_services(self, stack_name: str) -> List[Dict]:
        """Get services for a stack"""
        try:
            url = f"{self.config.portainer_url}/api/endpoints/{self.config.endpoint_id}/docker/services"
            response = self._make_request("GET", url)
            
            if response.status_code != 200:
                return []
            
            services = response.json()
            stack_services = []
            
            for service in services:
                labels = service.get('Spec', {}).get('Labels', {})
                if labels.get('com.docker.stack.namespace') == stack_name:
                    stack_services.append(service)
            
            return stack_services
            
        except Exception as e:
            self.logger.logger.warning(f"Could not get stack services: {e}")
            return []


class ComposeValidator:
    """Validates Docker Compose files"""
    
    def __init__(self, logger: DeploymentLogger):
        self.logger = logger
    
    def validate_compose_file(self, compose_file: Path, required_services: List[str] = None) -> bool:
        """Validate Docker Compose file"""
        try:
            with open(compose_file) as f:
                compose_data = yaml.safe_load(f)
            
            # Basic structure validation
            if not isinstance(compose_data, dict):
                self.logger.print_colored(Colors.RED, "Invalid compose file: not a dictionary", "error")
                return False
            
            if 'services' not in compose_data:
                self.logger.print_colored(Colors.RED, "Invalid compose file: no services section", "error")
                return False
            
            services = compose_data['services']
            if not isinstance(services, dict) or not services:
                self.logger.print_colored(Colors.RED, "Invalid compose file: services section empty", "error")
                return False
            
            # Check required services
            if required_services:
                missing_services = set(required_services) - set(services.keys())
                if missing_services:
                    self.logger.print_colored(
                        Colors.RED, 
                        f"Missing required services: {missing_services}",
                        "error"
                    )
                    return False
            
            # Validate service definitions
            for service_name, service_config in services.items():
                if not isinstance(service_config, dict):
                    self.logger.print_colored(
                        Colors.RED, 
                        f"Invalid service definition for {service_name}",
                        "error"
                    )
                    return False
                
                # Check for required fields
                if 'image' not in service_config and 'build' not in service_config:
                    self.logger.print_colored(
                        Colors.RED, 
                        f"Service {service_name} has no image or build configuration",
                        "error"
                    )
                    return False
            
            self.logger.print_colored(Colors.GREEN, f"Compose file validation passed: {len(services)} services")
            return True
            
        except yaml.YAMLError as e:
            self.logger.print_colored(Colors.RED, f"YAML parsing error: {e}", "error")
            return False
        except Exception as e:
            self.logger.print_colored(Colors.RED, f"Validation error: {e}", "error")
            return False


class HealthChecker:
    """Health check for deployed stacks"""
    
    def __init__(self, api_client: PortainerAPIClient, logger: DeploymentLogger):
        self.api_client = api_client
        self.logger = logger
        self.config = api_client.config
    
    def wait_for_stack_health(self, stack_name: str) -> bool:
        """Wait for stack to become healthy"""
        if not self.config.health_check_enabled:
            return True
        
        self.logger.print_colored(Colors.BLUE, f"Waiting for stack {stack_name} to become healthy...")
        
        start_time = time.time()
        
        while time.time() - start_time < self.config.health_check_timeout:
            try:
                services = self.api_client.get_stack_services(stack_name)
                
                if not services:
                    self.logger.print_colored(Colors.YELLOW, "No services found, waiting...", "warning")
                    time.sleep(self.config.health_check_interval)
                    continue
                
                healthy_services = 0
                total_services = len(services)
                
                for service in services:
                    service_name = service.get('Spec', {}).get('Name', 'unknown')
                    
                    # Check if service has running tasks
                    if self._is_service_healthy(service):
                        healthy_services += 1
                    else:
                        self.logger.print_colored(
                            Colors.YELLOW, 
                            f"Service {service_name} not yet healthy",
                            "warning"
                        )
                
                self.logger.print_colored(
                    Colors.BLUE, 
                    f"Health check: {healthy_services}/{total_services} services healthy"
                )
                
                if healthy_services == total_services:
                    self.logger.print_colored(Colors.GREEN, f"Stack {stack_name} is healthy!")
                    return True
                
                time.sleep(self.config.health_check_interval)
                
            except Exception as e:
                self.logger.print_colored(
                    Colors.YELLOW, 
                    f"Health check error: {e}",
                    "warning"
                )
                time.sleep(self.config.health_check_interval)
        
        self.logger.print_colored(
            Colors.RED, 
            f"Health check timeout for stack {stack_name}",
            "error"
        )
        return False
    
    def _is_service_healthy(self, service: Dict) -> bool:
        """Check if individual service is healthy"""
        try:
            # For now, just check if service exists
            # In a more sophisticated implementation, we could check:
            # - Running task count vs desired
            # - Health check status
            # - Service state
            return True
        except Exception:
            return False


class EnhancedPortainerDeployer:
    """Enhanced Portainer deployment manager"""
    
    def __init__(self, config: DeploymentConfig, logger: DeploymentLogger, dry_run: bool = False):
        self.config = config
        self.logger = logger
        self.dry_run = dry_run
        
        # Initialize components
        self.api_client = PortainerAPIClient(config, logger)
        self.validator = ComposeValidator(logger)
        self.health_checker = HealthChecker(self.api_client, logger)
        
        # Statistics
        self.deployment_stats = {
            'total': 0,
            'successful': 0,
            'failed': 0,
            'updated': 0,
            'created': 0,
            'skipped': 0
        }
    
    def load_config_from_env(self) -> bool:
        """Load configuration from environment file"""
        try:
            if not self.config.global_env_path.exists():
                self.logger.print_colored(
                    Colors.RED, 
                    f"Global .env file not found: {self.config.global_env_path}",
                    "error"
                )
                return False
            
            load_dotenv(self.config.global_env_path)
            
            # Load required environment variables
            api_key = os.getenv("PORTAINER_API_KEY")
            host = os.getenv("PORTAINER_HOST")
            
            if not api_key or not host:
                self.logger.print_colored(
                    Colors.RED, 
                    "Missing PORTAINER_API_KEY or PORTAINER_HOST in environment",
                    "error"
                )
                return False
            
            # Update configuration
            self.config.portainer_api_key = api_key
            self.config.portainer_host = host
            self.config.portainer_url = f"https://{host}:9443"
            
            # Optional environment variables
            if os.getenv("ENDPOINT_ID"):
                self.config.endpoint_id = os.getenv("ENDPOINT_ID")
            
            self.logger.print_colored(Colors.GREEN, "Configuration loaded successfully")
            return True
            
        except Exception as e:
            self.logger.print_colored(Colors.RED, f"Failed to load configuration: {e}", "error")
            return False
    
    def parse_env_file(self, env_file_path: Path) -> List[Dict[str, str]]:
        """Parse environment variables from .env file"""
        env_vars = []
        
        if not env_file_path.exists():
            self.logger.print_colored(Colors.YELLOW, f"No .env file found: {env_file_path}", "warning")
            return env_vars
        
        try:
            with open(env_file_path, 'r') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # Skip empty lines and comments
                    if not line or line.startswith('#'):
                        continue
                    
                    if '=' not in line:
                        self.logger.print_colored(
                            Colors.YELLOW, 
                            f"Invalid line {line_num} in {env_file_path}: {line}",
                            "warning"
                        )
                        continue
                    
                    key, value = line.split('=', 1)
                    env_vars.append({
                        "name": key.strip(), 
                        "value": value.strip()
                    })
            
            self.logger.print_colored(Colors.BLUE, f"Loaded {len(env_vars)} environment variables")
            return env_vars
            
        except Exception as e:
            self.logger.print_colored(
                Colors.RED, 
                f"Failed to parse env file {env_file_path}: {e}",
                "error"
            )
            return []
    
    def find_compose_file(self, customer_dir: Path) -> Optional[Path]:
        """Find docker-compose file in customer directory"""
        possible_names = ["docker-compose.yaml", "docker-compose.yml"]
        
        for name in possible_names:
            compose_file = customer_dir / name
            if compose_file.exists():
                return compose_file
        
        return None
    
    @DeploymentLogger.time_operation("single_deployment")
    def deploy_single_stack(self, customer_name: str) -> bool:
        """Deploy a single stack for a customer"""
        self.deployment_stats['total'] += 1
        
        # Build paths
        customer_dir = self.config.tenants_base / customer_name
        stack_name = f"{self.config.stack_prefix}-{customer_name}"
        
        self.logger.print_colored(Colors.CYAN, f"\n=== Deploying {customer_name} ===")
        
        # Validate customer directory
        if not customer_dir.is_dir():
            self.logger.print_colored(
                Colors.RED, 
                f"Customer directory not found: {customer_dir}",
                "error"
            )
            self.deployment_stats['failed'] += 1
            return False
        
        # Find compose file
        compose_file = self.find_compose_file(customer_dir)
        if not compose_file:
            self.logger.print_colored(
                Colors.RED, 
                f"No docker-compose file found in {customer_dir}",
                "error"
            )
            self.deployment_stats['failed'] += 1
            return False
        
        self.logger.print_colored(Colors.BLUE, f"Using compose file: {compose_file}")
        
        # Validate compose file
        if self.config.validate_compose_files:
            if not self.validator.validate_compose_file(compose_file, self.config.required_services):
                self.logger.print_colored(
                    Colors.RED, 
                    "Compose file validation failed",
                    "error"
                )
                self.deployment_stats['failed'] += 1
                return False
        
        # Parse environment variables
        env_file = customer_dir / ".env"
        env_vars = self.parse_env_file(env_file)
        
        if self.dry_run:
            self.logger.print_colored(Colors.MAGENTA, "DRY RUN: Would deploy stack")
            self.deployment_stats['skipped'] += 1
            return True
        
        try:
            # Check if stack exists
            existing_stack = self.api_client.get_stack_by_name(stack_name)
            
            if existing_stack:
                # Update existing stack
                self.logger.print_colored(
                    Colors.YELLOW, 
                    f"Stack {stack_name} exists (ID: {existing_stack['Id']}). Updating..."
                )
                
                # Read compose content for update
                compose_content = compose_file.read_text()
                
                success = self.api_client.update_stack(
                    existing_stack['Id'], 
                    compose_content, 
                    env_vars
                )
                
                if success:
                    self.deployment_stats['updated'] += 1
                    self.deployment_stats['successful'] += 1
                    
                    # Health check
                    if self.config.health_check_enabled:
                        if not self.health_checker.wait_for_stack_health(stack_name):
                            self.logger.print_colored(
                                Colors.YELLOW, 
                                "Stack deployed but health check failed",
                                "warning"
                            )
                    
                    return True
                else:
                    # If update fails, try delete and recreate
                    self.logger.print_colored(
                        Colors.YELLOW, 
                        "Update failed, trying delete and recreate..."
                    )
                    
                    if self.api_client.delete_stack(existing_stack['Id']):
                        time.sleep(3)  # Wait for cleanup
                        
                        success = self.api_client.create_stack(stack_name, compose_file, env_vars)
                        if success:
                            self.deployment_stats['created'] += 1
                            self.deployment_stats['successful'] += 1
                            
                            # Health check
                            if self.config.health_check_enabled:
                                self.health_checker.wait_for_stack_health(stack_name)
                            
                            return True
                    
                    self.deployment_stats['failed'] += 1
                    return False
            
            else:
                # Create new stack
                self.logger.print_colored(Colors.BLUE, f"Creating new stack: {stack_name}")
                
                success = self.api_client.create_stack(stack_name, compose_file, env_vars)
                
                if success:
                    self.deployment_stats['created'] += 1
                    self.deployment_stats['successful'] += 1
                    
                    # Health check
                    if self.config.health_check_enabled:
                        self.health_checker.wait_for_stack_health(stack_name)
                    
                    return True
                else:
                    self.deployment_stats['failed'] += 1
                    return False
        
        except Exception as e:
            self.logger.print_colored(Colors.RED, f"Deployment error: {e}", "error")
            self.deployment_stats['failed'] += 1
            return False
    
    def deploy_multiple_stacks(self, customer_names: List[str]) -> Dict[str, bool]:
        """Deploy multiple stacks in parallel"""
        self.logger.print_colored(
            Colors.CYAN, 
            f"Deploying {len(customer_names)} stacks in parallel..."
        )
        
        results = {}
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.config.parallel_deployments) as executor:
            # Submit all deployment tasks
            future_to_customer = {
                executor.submit(self.deploy_single_stack, customer): customer
                for customer in customer_names
            }
            
            # Process results with progress bar
            with tqdm(total=len(customer_names), desc="Deploying stacks") as pbar:
                for future in concurrent.futures.as_completed(future_to_customer):
                    customer = future_to_customer[future]
                    try:
                        success = future.result()
                        results[customer] = success
                        
                        status = "✓" if success else "✗"
                        pbar.set_postfix_str(f"Last: {customer} {status}")
                        pbar.update(1)
                        
                    except Exception as e:
                        self.logger.print_colored(
                            Colors.RED, 
                            f"Exception for {customer}: {e}",
                            "error"
                        )
                        results[customer] = False
                        pbar.update(1)
        
        return results
    
    def get_available_customers(self) -> List[str]:
        """Get list of available customers from tenants directory"""
        try:
            customers = []
            for item in self.config.tenants_base.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    # Check if it has a compose file
                    if self.find_compose_file(item):
                        customers.append(item.name)
            
            return sorted(customers)
            
        except Exception as e:
            self.logger.print_colored(Colors.RED, f"Failed to get customers: {e}", "error")
            return []
    
    def print_deployment_summary(self):
        """Print deployment statistics summary"""
        stats = self.deployment_stats
        
        self.logger.print_colored(Colors.CYAN, "\n=== Deployment Summary ===")
        self.logger.print_colored(Colors.WHITE, f"Total deployments: {stats['total']}")
        self.logger.print_colored(Colors.GREEN, f"Successful: {stats['successful']}")
        self.logger.print_colored(Colors.RED, f"Failed: {stats['failed']}")
        self.logger.print_colored(Colors.BLUE, f"Created: {stats['created']}")
        self.logger.print_colored(Colors.YELLOW, f"Updated: {stats['updated']}")
        
        if stats['skipped'] > 0:
            self.logger.print_colored(Colors.MAGENTA, f"Skipped (dry-run): {stats['skipped']}")
        
        success_rate = (stats['successful'] / stats['total'] * 100) if stats['total'] > 0 else 0
        self.logger.print_colored(Colors.CYAN, f"Success rate: {success_rate:.1f}%")


def load_deployment_config(config_path: str = None) -> DeploymentConfig:
    """Load deployment configuration"""
    if config_path:
        config_file = Path(config_path)
    else:
        # Look for config in common locations
        possible_configs = [
            Path("portainer_deploy.yaml"),
            Path("/etc/portainer_deploy.yaml"),
            Path.home() / ".config" / "portainer_deploy.yaml"
        ]
        
        config_file = None
        for config_path in possible_configs:
            if config_path.exists():
                config_file = config_path
                break
    
    if config_file and config_file.exists():
        return DeploymentConfig.from_file(config_file)
    else:
        return DeploymentConfig()


def main():
    """Enhanced main function"""
    parser = argparse.ArgumentParser(
        description='Enhanced Portainer Stack Deployment',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s acme-corp                    # Deploy single customer
  %(prog)s --all                        # Deploy all customers
  %(prog)s acme-corp test-client        # Deploy multiple customers
  %(prog)s --all --dry-run              # Test all deployments
  %(prog)s acme-corp --health-check     # Deploy with health checks
  %(prog)s --list                       # List available customers
        """
    )
    
    parser.add_argument('customers', nargs='*', help='Customer names to deploy')
    parser.add_argument('--all', action='store_true', help='Deploy all available customers')
    parser.add_argument('--list', action='store_true', help='List available customers and exit')
    parser.add_argument('--dry-run', action='store_true', help='Perform dry run without actual deployment')
    parser.add_argument('--config', help='Path to configuration file')
    parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'])
    parser.add_argument('--log-file', help='Path to log file')
    parser.add_argument('--parallel', type=int, help='Number of parallel deployments')
    parser.add_argument('--no-health-check', action='store_true', help='Disable health checks')
    parser.add_argument('--validate', action='store_true', help='Only validate compose files')
    parser.add_argument('--version', action='version', version='Enhanced Portainer Deployer v2.0')
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_deployment_config(args.config)
    
    # Override config with command line arguments
    if args.parallel:
        config.parallel_deployments = args.parallel
    if args.no_health_check:
        config.health_check_enabled = False
    
    # Setup logging
    log_file = Path(args.log_file) if args.log_file else None
    logger = DeploymentLogger(args.log_level, log_file)
    
    # Create deployer
    deployer = EnhancedPortainerDeployer(config, logger, args.dry_run)
    
    try:
        # Load configuration from environment
        if not deployer.load_config_from_env():
            sys.exit(1)
        
        logger.print_colored(Colors.GREEN, f"Using Portainer: {config.portainer_url}")
        logger.print_colored(Colors.BLUE, f"Endpoint ID: {config.endpoint_id}")
        
        if args.dry_run:
            logger.print_colored(Colors.MAGENTA, "=== DRY RUN MODE ===")
        
        # List customers and exit
        if args.list:
            customers = deployer.get_available_customers()
            logger.print_colored(Colors.CYAN, f"Available customers ({len(customers)}):")
            for customer in customers:
                logger.print_colored(Colors.WHITE, f"  - {customer}")
            sys.exit(0)
        
        # Determine customers to deploy
        if args.all:
            customers = deployer.get_available_customers()
            if not customers:
                logger.print_colored(Colors.RED, "No customers found", "error")
                sys.exit(1)
        elif args.customers:
            customers = args.customers
        else:
            parser.print_help()
            sys.exit(1)
        
        logger.print_colored(Colors.CYAN, f"Deploying: {', '.join(customers)}")
        
        # Validation mode
        if args.validate:
            logger.print_colored(Colors.BLUE, "Validation mode - checking compose files...")
            validator = ComposeValidator(logger)
            
            for customer in customers:
                customer_dir = config.tenants_base / customer
                compose_file = deployer.find_compose_file(customer_dir)
                
                if compose_file:
                    logger.print_colored(Colors.CYAN, f"Validating {customer}...")
                    validator.validate_compose_file(compose_file, config.required_services)
                else:
                    logger.print_colored(Colors.RED, f"No compose file for {customer}", "error")
            
            sys.exit(0)
        
        # Deploy stacks
        if len(customers) == 1:
            # Single deployment
            success = deployer.deploy_single_stack(customers[0])
            deployer.print_deployment_summary()
            logger.log_performance_summary()
            
            if not success:
                sys.exit(1)
        else:
            # Multiple deployments
            results = deployer.deploy_multiple_stacks(customers)
            deployer.print_deployment_summary()
            logger.log_performance_summary()
            
            # Check if any deployments failed
            failed_customers = [customer for customer, success in results.items() if not success]
            if failed_customers:
                logger.print_colored(
                    Colors.RED, 
                    f"Failed deployments: {', '.join(failed_customers)}",
                    "error"
                )
                sys.exit(1)
        
        logger.print_colored(Colors.GREEN, "All deployments completed successfully!")
        
    except KeyboardInterrupt:
        logger.print_colored(Colors.YELLOW, "\nDeployment interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.print_colored(Colors.RED, f"Fatal error: {e}", "error")
        sys.exit(1)


if __name__ == "__main__":
    main()