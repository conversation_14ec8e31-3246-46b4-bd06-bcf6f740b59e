import os
import logging
import requests
import sys
import time
import argparse
from dotenv import load_dotenv

# Laden der Umgebungsvariablen aus der .env-Datei
load_dotenv()

# Konfiguration der Umgebungsvariablen
API_URL = os.getenv("API_URL")
API_KEY = os.getenv("API_KEY")
ZONE_NAME = os.getenv("ZONE_NAME")

# Setze den Logger
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Prüfe, ob alle Umgebungsvariablen gesetzt sind
if not API_URL or not API_KEY or not ZONE_NAME:
    logging.error("API_URL, API_KEY und ZONE_NAME müssen gesetzt sein!")
    exit(1)

# Funktion, um zu prüfen, ob ein DNS-Eintrag bereits existiert
def check_dns_record_exists(record_name, record_type):
    url = f"{API_URL}/zones/{ZONE_NAME}"
    headers = {
        "X-API-Key": API_KEY,
        "Accept": "application/json"
    }
    
    try:
        # Hole alle Records der Zone
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        zone_data = response.json()
        
        # Suche nach dem spezifischen Record
        if "rrsets" in zone_data:
            for rrset in zone_data["rrsets"]:
                if rrset["name"] == record_name and rrset["type"] == record_type:
                    for record in rrset["records"]:
                        if not record["disabled"]:
                            logging.info(f"DNS-Eintrag {record_name} ({record_type}) existiert bereits: {record['content']}")
                            return True, record["content"]
        
        return False, None
    except requests.exceptions.RequestException as e:
        logging.error(f"Fehler beim Prüfen des DNS-Eintrags: {e}")
        return False, None

# Funktion, um den DNS-Eintrag zu erstellen
def update_dns(record_name, record_type, record_content):
    # Entferne Leerzeichen aus dem record_name
    record_name = record_name.replace(" ", "")
    
    # Prüfe, ob der Eintrag bereits existiert
    exists, existing_content = check_dns_record_exists(record_name, record_type)
    if exists:
        # Wenn der Eintrag bereits mit demselben Inhalt existiert, nichts tun
        if existing_content == record_content:
            logging.info(f"DNS-Eintrag {record_name} existiert bereits mit richtigem Inhalt. Keine Änderung notwendig.")
            return True, False  # Erfolg, aber keine Änderung
        # Wenn der Eintrag mit anderem Inhalt existiert, Benutzer informieren
        logging.info(f"DNS-Eintrag {record_name} existiert bereits, aber mit anderem Inhalt: {existing_content}")
        logging.info(f"Aktualisiere auf: {record_content}")
    
    url = f"{API_URL}/zones/{ZONE_NAME}"
    
    # Erstellen oder Aktualisieren der DNS-Daten
    data = {
        "rrsets": [
            {
                "name": record_name,
                "type": record_type,
                "ttl": 3600,
                "changetype": "REPLACE",
                "records": [
                    {"content": record_content, "disabled": False}
                ]
            }
        ]
    }
    
    headers = {
        "X-API-Key": API_KEY,
        "Content-Type": "application/json"
    }
    
    # HTTP PATCH-Anfrage ausführen
    try:
        logging.info(f"Versuche, DNS-Eintrag für {record_name} zu {'aktualisieren' if exists else 'erstellen'}...")
        response = requests.patch(url, json=data, headers=headers)
        response.raise_for_status()
        logging.info(f"Erfolgreich: DNS-Eintrag für {record_name} wurde {'aktualisiert' if exists else 'erstellt'}!")
        return True, True  # Erfolg mit Änderung
    except requests.exceptions.RequestException as e:
        logging.error(f"Fehler bei der Anfrage: {e}")
        return False, False  # Fehler, keine Änderung

# Funktion zum Löschen eines DNS-Eintrags
def delete_dns(record_name, record_type):
    # Entferne Leerzeichen aus dem record_name
    record_name = record_name.replace(" ", "")
    
    # Prüfe, ob der Eintrag existiert
    exists, _ = check_dns_record_exists(record_name, record_type)
    if not exists:
        logging.info(f"DNS-Eintrag {record_name} ({record_type}) existiert nicht. Nichts zu löschen.")
        return True, False  # Erfolg, aber keine Änderung
    
    url = f"{API_URL}/zones/{ZONE_NAME}"
    
    # DNS-Daten zum Löschen
    data = {
        "rrsets": [
            {
                "name": record_name,
                "type": record_type,
                "changetype": "DELETE",
                "records": []
            }
        ]
    }
    
    headers = {
        "X-API-Key": API_KEY,
        "Content-Type": "application/json"
    }
    
    # HTTP PATCH-Anfrage ausführen
    try:
        logging.info(f"Versuche, DNS-Eintrag {record_name} ({record_type}) zu löschen...")
        response = requests.patch(url, json=data, headers=headers)
        response.raise_for_status()
        logging.info(f"Erfolgreich: DNS-Eintrag {record_name} ({record_type}) wurde gelöscht!")
        return True, True  # Erfolg mit Änderung
    except requests.exceptions.RequestException as e:
        logging.error(f"Fehler beim Löschen des DNS-Eintrags: {e}")
        return False, False  # Fehler, keine Änderung

# Funktion zum Senden einer NOTIFY-Anfrage
def notify_zone():
    url = f"{API_URL}/zones/{ZONE_NAME}/notify"
    headers = {
        "X-API-Key": API_KEY,
        "Content-Type": "application/json"
    }
    
    try:
        logging.info(f"Sende NOTIFY für Zone {ZONE_NAME}...")
        response = requests.put(url, headers=headers)
        response.raise_for_status()
        logging.info(f"NOTIFY für Zone {ZONE_NAME} erfolgreich gesendet!")
        return True
    except requests.exceptions.RequestException as e:
        logging.error(f"Fehler beim Senden von NOTIFY: {e}")
        return False

# Funktion zum Rectify der Zone (korrigiert DNSSEC-Records)
def rectify_zone():
    url = f"{API_URL}/zones/{ZONE_NAME}/rectify"
    headers = {
        "X-API-Key": API_KEY,
        "Content-Type": "application/json"
    }
    
    try:
        logging.info(f"Führe Rectify für Zone {ZONE_NAME} durch...")
        response = requests.put(url, headers=headers)
        response.raise_for_status()
        logging.info(f"Rectify für Zone {ZONE_NAME} erfolgreich!")
        return True
    except requests.exceptions.RequestException as e:
        logging.error(f"Fehler beim Rectify: {e}")
        return False

# Funktion zum Aktivieren von Änderungen (wenn Änderungen vorgenommen wurden)
def activate_changes():
    success_actions = []
    
    # 1. Rectify Zone
    if rectify_zone():
        success_actions.append("rectify")
    
    # 2. Notify
    if notify_zone():
        success_actions.append("notify")
    
    # Kurze Pause für eventuelle asynchrone Verarbeitung
    time.sleep(1)
    
    return success_actions

# Hauptfunktion
if __name__ == "__main__":
    # Argument-Parser erstellen
    parser = argparse.ArgumentParser(description="PowerDNS Einträge verwalten")
    parser.add_argument("customer", help="Name des Kunden")
    parser.add_argument("-d", "--delete", action="store_true", help="DNS-Einträge löschen statt erstellen/aktualisieren")
    
    args = parser.parse_args()
    customer_name = args.customer.strip()
    
    # DNS-Eintragsnamen für den Kunden
    records = [
        f"{customer_name}.leos360.cloud.",
        f"{customer_name}-sso.leos360.cloud.",
        f"{customer_name}-api.leos360.cloud."
    ]
    
    all_successful = True
    any_changes = False
    
    # Je nach Modus Einträge erstellen/aktualisieren oder löschen
    if args.delete:
        # Einträge löschen
        for record in records:
            success, changed = delete_dns(record, "CNAME")
            all_successful = all_successful and success
            any_changes = any_changes or changed
        
        action_type = "Löschen"
    else:
        # Einträge erstellen/aktualisieren
        for record in records:
            success, changed = update_dns(record, "CNAME", "webproxy01.leos360.cloud.")
            all_successful = all_successful and success
            any_changes = any_changes or changed
        
        action_type = "Erstellen/Aktualisieren"
    
    # Nur wenn alle Operationen erfolgreich waren
    if all_successful:
        # Nur wenn Änderungen vorgenommen wurden, Zone neu laden
        if any_changes:
            success_actions = activate_changes()
            
            if "rectify" in success_actions and "notify" in success_actions:
                logging.info(f"DNS-Einträge für {customer_name} erfolgreich {action_type.lower()}t und aktiviert!")
                logging.info(f"Erfolgreiche Aktionen: {', '.join(success_actions)}")
                exit(0)
            elif success_actions:
                logging.warning(f"DNS-Einträge {action_type.lower()}t, aber nur teilweise aktiviert: {', '.join(success_actions)}")
                exit(1)
            else:
                logging.error(f"DNS-Einträge wurden {action_type.lower()}t, aber es gab Probleme beim Aktivieren.")
                exit(1)
        else:
            logging.info(f"Alle DNS-Einträge für {customer_name} bereits im gewünschten Zustand.")
            logging.info("Keine Änderungen vorgenommen, kein Reload notwendig.")
            exit(0)
    else:
        logging.error(f"Nicht alle DNS-Einträge konnten {action_type.lower()}t werden.")
        exit(1)