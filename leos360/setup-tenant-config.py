#!/usr/bin/env python3.12
import os
import sys
import re
import shutil
import subprocess
import argparse
import secrets
import string
import base64
import datetime
import time
import json
import logging
import yaml
import concurrent.futures
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any
from tqdm import tqdm
import hashlib


@dataclass
class Config:
    """Configuration class for tenant setup"""
    # Network configuration
    ip_range_start: int = 20
    ip_range_end: int = 150
    ip_prefix: str = "172.16.7."
    
    # Database configuration
    external_db_host: str = "************"
    external_db_port: str = "5432"
    
    # Password configuration
    password_length: int = 32
    exclude_chars: str = "=+/"
    
    # Paths
    master_base: Path = Path("/mnt/storage/setup")
    tenants_base: Path = Path("/mnt/storage/tenants")
    
    # Domain configuration
    base_domain: str = "leos360.cloud"
    
    # Permissions
    permissions: Dict[str, int] = None
    
    def __post_init__(self):
        if self.permissions is None:
            self.permissions = {
                'secrets': 0o600,
                'configs': 0o640,
                'scripts': 0o750,
                'directories': 0o750,
                'regular': 0o644
            }
    
    @classmethod
    def from_file(cls, config_path: Path) -> 'Config':
        """Load configuration from YAML file"""
        if not config_path.exists():
            return cls()  # Return default config
        
        with open(config_path) as f:
            data = yaml.safe_load(f)
        
        config = cls()
        
        # Update configuration from file
        if 'networking' in data:
            net = data['networking']
            if 'ip_range' in net:
                config.ip_range_start = net['ip_range'].get('start', config.ip_range_start)
                config.ip_range_end = net['ip_range'].get('end', config.ip_range_end)
                config.ip_prefix = net['ip_range'].get('prefix', config.ip_prefix)
        
        if 'database' in data:
            db = data['database']
            config.external_db_host = db.get('host', config.external_db_host)
            config.external_db_port = db.get('port', config.external_db_port)
        
        if 'password_policy' in data:
            pwd = data['password_policy']
            config.password_length = pwd.get('length', config.password_length)
            config.exclude_chars = pwd.get('exclude_chars', config.exclude_chars)
        
        if 'paths' in data:
            paths = data['paths']
            config.master_base = Path(paths.get('master_base', config.master_base))
            config.tenants_base = Path(paths.get('tenants_base', config.tenants_base))
        
        return config


class SetupLogger:
    """Enhanced logging for tenant setup"""
    
    def __init__(self, customer_name: str, log_level: str = "INFO"):
        self.customer_name = customer_name
        self.logger = logging.getLogger(f"tenant_setup.{customer_name}")
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # Setup handlers if not already configured
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """Setup logging handlers"""
        # Console handler
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # File handler
        log_dir = Path("/var/log/tenant_setup")
        log_dir.mkdir(exist_ok=True, parents=True)
        file_handler = logging.FileHandler(
            log_dir / f"{self.customer_name}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        )
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
    
    def log_action(self, action: str, status: str, details: Any = None, duration: float = None):
        """Log structured action data"""
        log_data = {
            "timestamp": datetime.datetime.now().isoformat(),
            "customer": self.customer_name,
            "action": action,
            "status": status,
            "details": details,
            "duration_seconds": duration
        }
        
        log_message = f"Action: {action} | Status: {status}"
        if duration:
            log_message += f" | Duration: {duration:.2f}s"
        if details:
            log_message += f" | Details: {details}"
        
        if status.upper() == "SUCCESS":
            self.logger.info(log_message)
        elif status.upper() == "ERROR":
            self.logger.error(log_message)
        else:
            self.logger.warning(log_message)
    
    def info(self, message: str):
        self.logger.info(message)
    
    def error(self, message: str):
        self.logger.error(message)
    
    def warning(self, message: str):
        self.logger.warning(message)


class TenantSetupError(Exception):
    """Custom exception for tenant setup errors"""
    pass


class TemplateError(TenantSetupError):
    """Template-related errors"""
    pass


class ValidationError(TenantSetupError):
    """Validation-related errors"""
    pass


class EnhancedTenantConfigSetup:
    """Enhanced tenant configuration setup with optimizations"""
    
    def __init__(self, customer_name: str, config: Config = None, dry_run: bool = False):
        self.customer_name = customer_name
        self.config = config or Config()
        self.dry_run = dry_run
        self.logger = SetupLogger(customer_name)
        
        # Initialize paths and configuration
        self._initialize_paths()
        self._initialize_variables()
        
        # Rollback actions for cleanup
        self.rollback_actions: List[Tuple[callable, tuple, dict]] = []
        
        # Performance tracking
        self.start_time = time.time()
        self.step_times = {}
    
    def _initialize_paths(self):
        """Initialize all paths based on configuration"""
        self.master_base = self.config.master_base
        self.ssl_base = self.master_base / "ssl"
        self.keycloak_realm = f"{self.customer_name}.{self.config.base_domain}"
        
        # Customer specific configuration
        self.customer_domain = f"{self.customer_name}.{self.config.base_domain}"
        self.config_base = self.config.tenants_base / self.customer_name
        
        # Service paths
        self.service_paths = {
            'dovecot': self.config_base / "dovecot",
            'lldap': self.config_base / "lldap",
            'keycloak': self.config_base / "keycloak",
            'postfix': self.config_base / "postfix",
            'db': self.config_base / "db",
            'redis': self.config_base / "redis",
            'nextcloud': self.config_base / "nextcloud",
        }
    
    def _initialize_variables(self):
        """Initialize configuration variables"""
        # Network configuration
        self.customer_ip = ""
        self.redis_db_index = ""
        
        # Generated secrets
        self.secrets = {}
        
        # Cache for IP allocation
        self.ip_cache_file = Path("/tmp/tenant_ip_cache.json")
        self.ip_cache_timeout = 300  # 5 minutes
    
    def add_rollback_action(self, action: callable, *args, **kwargs):
        """Add action to rollback list"""
        self.rollback_actions.append((action, args, kwargs))
    
    def _time_step(self, step_name: str):
        """Decorator to time steps"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                start = time.time()
                try:
                    result = func(*args, **kwargs)
                    duration = time.time() - start
                    self.step_times[step_name] = duration
                    self.logger.log_action(step_name, "SUCCESS", duration=duration)
                    return result
                except Exception as e:
                    duration = time.time() - start
                    self.logger.log_action(step_name, "ERROR", str(e), duration)
                    raise
            return wrapper
        return decorator
    
    def validate_customer_name(self, name: str):
        """Enhanced customer name validation"""
        if not name:
            raise ValidationError("Customer name cannot be empty")
        
        if len(name) < 2:
            raise ValidationError("Customer name must be at least 2 characters")
        
        if len(name) > 50:
            raise ValidationError("Customer name cannot exceed 50 characters")
        
        if not re.match(r'^[a-z0-9-]+$', name):
            raise ValidationError("Customer name must contain only lowercase letters, numbers and hyphens")
        
        if name.startswith('-') or name.endswith('-'):
            raise ValidationError("Customer name cannot start or end with hyphen")
        
        if '--' in name:
            raise ValidationError("Customer name cannot contain consecutive hyphens")
        
        # Reserved names
        reserved_names = {
            'admin', 'root', 'system', 'www', 'ftp', 'mail', 'email',
            'api', 'app', 'web', 'service', 'test', 'demo', 'staging'
        }
        
        if name in reserved_names:
            raise ValidationError(f"Customer name '{name}' is reserved")
    
    def validate_templates(self):
        """Validate template integrity before setup"""
        self.logger.info("Validating templates...")
        
        required_templates = [
            self.master_base / ".env",
            self.master_base / "docker-compose.yml",
            self.master_base / "db" / "db_setup.sql.template",
            self.master_base / "dovecot" / "dovecot-ldap-userdb.conf",
            self.master_base / "dovecot" / "dovecot-ldap-passdb.conf",
            self.master_base / "postfix" / "ldap" / "virtual_aliases.cf",
            self.master_base / "postfix" / "ldap" / "virtual_domains.cf",
        ]
        
        missing_templates = []
        for template in required_templates:
            if not template.exists():
                missing_templates.append(str(template))
        
        if missing_templates:
            raise TemplateError(f"Missing required templates: {missing_templates}")
        
        # Validate template syntax
        for template in required_templates:
            if template.suffix in ['.conf', '.cf', '.template']:
                self._validate_template_variables(template)
    
    def _validate_template_variables(self, template_path: Path):
        """Validate that template variables are properly formatted"""
        try:
            content = template_path.read_text()
            
            # Find all ${VAR} patterns
            var_pattern = re.compile(r'\$\{([^}]+)\}')
            variables = var_pattern.findall(content)
            
            # Check for malformed variables
            malformed = []
            for var in variables:
                if not re.match(r'^[A-Z_][A-Z0-9_]*$', var):
                    malformed.append(var)
            
            if malformed:
                self.logger.warning(f"Template {template_path} contains malformed variables: {malformed}")
        
        except Exception as e:
            self.logger.warning(f"Could not validate template {template_path}: {e}")
    
    def setup_nextfreeip_optimized(self):
        """Optimized IP allocation with caching"""
        self.logger.info("Getting next free IP address with caching...")
        start_time = time.time()
        
        # Try to use cache
        used_ips = self._get_cached_ips()
        if used_ips is None:
            used_ips = self._scan_used_ips_parallel()
            self._cache_used_ips(used_ips)
        
        # Find next available IP
        for i in range(self.config.ip_range_start, self.config.ip_range_end + 1):
            candidate_ip = f"{self.config.ip_prefix}{i}"
            if candidate_ip not in used_ips:
                self.customer_ip = candidate_ip
                self.redis_db_index = str(i)
                
                duration = time.time() - start_time
                self.logger.info(f"Allocated IP {candidate_ip} in {duration:.3f}s")
                return
        
        raise TenantSetupError("No available IP addresses found in configured range")
    
    def _get_cached_ips(self) -> Optional[set]:
        """Get cached IP addresses if cache is valid"""
        if not self.ip_cache_file.exists():
            return None
        
        cache_age = time.time() - self.ip_cache_file.stat().st_mtime
        if cache_age > self.ip_cache_timeout:
            return None
        
        try:
            with open(self.ip_cache_file) as f:
                data = json.load(f)
                return set(data.get('used_ips', []))
        except Exception as e:
            self.logger.warning(f"Failed to load IP cache: {e}")
            return None
    
    def _cache_used_ips(self, used_ips: set):
        """Cache used IP addresses"""
        try:
            cache_data = {
                'timestamp': time.time(),
                'used_ips': list(used_ips)
            }
            with open(self.ip_cache_file, 'w') as f:
                json.dump(cache_data, f)
        except Exception as e:
            self.logger.warning(f"Failed to cache IPs: {e}")
    
    def _scan_used_ips_parallel(self) -> set:
        """Scan used IPs with parallel processing"""
        search_path = str(self.config.tenants_base)
        
        # Get tenant directories
        try:
            tenant_dirs = [
                Path(search_path) / d for d in os.listdir(search_path)
                if (Path(search_path) / d).is_dir()
            ]
        except Exception as e:
            self.logger.error(f"Failed to list tenant directories: {e}")
            return set()
        
        used_ips = set()
        
        # Process .env files in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            future_to_dir = {
                executor.submit(self._extract_ip_from_env, tenant_dir): tenant_dir
                for tenant_dir in tenant_dirs
            }
            
            for future in concurrent.futures.as_completed(future_to_dir):
                tenant_dir = future_to_dir[future]
                try:
                    ip = future.result()
                    if ip:
                        used_ips.add(ip)
                except Exception as e:
                    self.logger.warning(f"Failed to process {tenant_dir}: {e}")
        
        return used_ips
    
    def _extract_ip_from_env(self, tenant_dir: Path) -> Optional[str]:
        """Extract IP from .env file"""
        env_file = tenant_dir / ".env"
        if not env_file.exists():
            return None
        
        try:
            with open(env_file) as f:
                for line in f:
                    if line.startswith('CUSTOMER_IP='):
                        return line.split('=', 1)[1].strip()
        except Exception:
            pass
        
        return None
    
    def check_prerequisites(self):
        """Enhanced prerequisite validation"""
        self.logger.info("Checking prerequisites...")
        
        # Check if running as root
        if os.geteuid() != 0:
            raise TenantSetupError("This script must be run as root")
        
        # Validate customer name
        self.validate_customer_name(self.customer_name)
        
        # Check required directories
        if not self.master_base.exists():
            raise TenantSetupError(f"Master directory {self.master_base} does not exist")
        
        if not self.ssl_base.exists():
            raise TenantSetupError(f"SSL directory {self.ssl_base} does not exist")
        
        # Check if customer directory already exists
        if self.config_base.exists() and not self.dry_run:
            raise TenantSetupError(f"Customer directory {self.config_base} already exists")
        
        # Validate templates
        self.validate_templates()
        
        # Check available disk space
        self._check_disk_space()
    
    def _check_disk_space(self, required_gb: float = 1.0):
        """Check available disk space"""
        try:
            stat = shutil.disk_usage(self.config.tenants_base)
            available_gb = stat.free / (1024**3)
            
            if available_gb < required_gb:
                raise TenantSetupError(
                    f"Insufficient disk space. Required: {required_gb}GB, Available: {available_gb:.2f}GB"
                )
            
            self.logger.info(f"Disk space check passed. Available: {available_gb:.2f}GB")
        except Exception as e:
            self.logger.warning(f"Could not check disk space: {e}")
    
    def generate_secure_password(self, length: int = None) -> str:
        """Enhanced secure password generation"""
        length = length or self.config.password_length
        
        # Use cryptographically secure random number generator
        random_bytes = secrets.token_bytes(length + 8)  # Extra bytes for filtering
        
        # Convert to base64 and filter out unwanted characters
        password = base64.b64encode(random_bytes).decode('utf-8')
        password = re.sub(f'[{re.escape(self.config.exclude_chars)}]', '', password)
        
        # Ensure minimum length
        while len(password) < length:
            additional_bytes = secrets.token_bytes(8)
            additional = base64.b64encode(additional_bytes).decode('utf-8')
            additional = re.sub(f'[{re.escape(self.config.exclude_chars)}]', '', additional)
            password += additional
        
        return password[:length]
    
    def generate_passwords(self):
        """Generate all required passwords"""
        self.logger.info("Generating secure passwords...")
        
        password_fields = [
            'db_password',
            'nextcloud_admin_password',
            'keycloak_admin_password',
            'lldap_jwt_secret',
            'lldap_key_seed',
            'lldap_admin_pass',
            'lldap_ro_pass',
            'signal_secret',
            'redis_secret',
            'nextcloud_keycloak_client_secret',
            'leos360portal_keycloak_client_secret'
        ]
        
        for field in password_fields:
            self.secrets[field] = self.generate_secure_password()
        
        # Save credentials
        self._save_credentials()
    
    def _save_credentials(self):
        """Save generated credentials securely"""
        if self.dry_run:
            self.logger.info("DRY RUN: Would save credentials")
            return
        
        secrets_dir = self.config_base / ".secrets"
        self.ensure_directory(secrets_dir, self.config.permissions['secrets'])
        
        credentials_content = f"""# Generated credentials for {self.customer_name}
# Generated on {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# DO NOT SHARE OR COMMIT TO VERSION CONTROL

Customer IP: {self.customer_ip}
Redis DB Index: {self.redis_db_index}

"""
        
        for key, value in self.secrets.items():
            display_name = key.replace('_', ' ').title()
            credentials_content += f"{display_name}: {value}\n"
        
        credentials_file = secrets_dir / "credentials.txt"
        credentials_file.write_text(credentials_content)
        os.chmod(credentials_file, self.config.permissions['secrets'])
        
        self.add_rollback_action(shutil.rmtree, secrets_dir, ignore_errors=True)
    
    def ensure_directory(self, directory: Path, permissions: int = None) -> Path:
        """Enhanced directory creation with proper permissions"""
        permissions = permissions or self.config.permissions['directories']
        
        if self.dry_run:
            self.logger.info(f"DRY RUN: Would create directory {directory} with permissions {oct(permissions)}")
            return directory
        
        directory.mkdir(parents=True, exist_ok=True)
        os.chmod(directory, permissions)
        
        self.add_rollback_action(shutil.rmtree, directory, ignore_errors=True)
        return directory
    
    def copy_and_set_permissions(self, source: Path, target: Path, permissions: int = None) -> bool:
        """Enhanced file copying with proper permissions"""
        permissions = permissions or self.config.permissions['regular']
        
        if not source.exists():
            self.logger.warning(f"Source file {source} not found")
            return False
        
        if self.dry_run:
            self.logger.info(f"DRY RUN: Would copy {source} to {target} with permissions {oct(permissions)}")
            return True
        
        # Ensure target directory exists
        target.parent.mkdir(parents=True, exist_ok=True)
        
        shutil.copy2(source, target)  # copy2 preserves metadata
        os.chmod(target, permissions)
        
        self.add_rollback_action(os.unlink, target)
        return True
    
    def replace_variables(self, content: str, replacements: Dict[str, str]) -> str:
        """Enhanced variable replacement with validation"""
        original_content = content
        changes_made = True
        iterations = 0
        max_iterations = 10  # Prevent infinite loops
        
        while changes_made and iterations < max_iterations:
            changes_made = False
            iterations += 1
            
            for placeholder, value in replacements.items():
                if placeholder.startswith("${") and placeholder.endswith("}"):
                    if placeholder in content:
                        new_content = content.replace(placeholder, str(value))
                        if new_content != content:
                            content = new_content
                            changes_made = True
        
        if iterations >= max_iterations:
            self.logger.warning("Variable replacement reached maximum iterations")
        
        # Log replacement statistics
        original_vars = len(re.findall(r'\$\{[^}]+\}', original_content))
        remaining_vars = len(re.findall(r'\$\{[^}]+\}', content))
        
        if remaining_vars > 0:
            remaining = re.findall(r'\$\{[^}]+\}', content)
            self.logger.warning(f"Unresolved variables remaining: {set(remaining)}")
        
        self.logger.info(f"Variable replacement: {original_vars - remaining_vars}/{original_vars} resolved")
        return content
    
    def create_directory_structure(self):
        """Create comprehensive directory structure"""
        self.logger.info(f"Creating directory structure for {self.customer_name}...")
        
        if self.dry_run:
            self.logger.info("DRY RUN: Would create directory structure")
            return
        
        # Base service directories
        for service_name, service_path in self.service_paths.items():
            self.ensure_directory(service_path)
        
        # Service-specific subdirectories
        subdirs = {
            'redis': ['data'],
            'nextcloud': ['data', 'config', 'custom_apps', 'templates', 'setup', 'html'],
            'keycloak': ['data', 'config'],
            'lldap': [
                'data',
                'config/bootstrap/group-configs',
                'config/bootstrap/group-schemas',
                'config/bootstrap/user-configs',
                'config/bootstrap/user-schemas'
            ],
            'dovecot': ['data/mail', 'config/conf.d', 'config/ldap', 'config/ssl', 'config/scripts'],
            'postfix': ['config/ldap', 'config/main.cf.d', 'data/spool']
        }
        
        for service, dirs in subdirs.items():
            service_base = self.service_paths[service]
            for subdir in dirs:
                self.ensure_directory(service_base / subdir)
        
        # Set base permissions
        os.chmod(self.config_base, self.config.permissions['directories'])
    
    def create_env_file(self):
        """Create enhanced .env file"""
        self.logger.info("Creating .env file...")
        
        env_file = self.config_base / ".env"
        
        if self.dry_run:
            self.logger.info(f"DRY RUN: Would create {env_file}")
            return
        
        # Copy master .env template
        self.copy_and_set_permissions(
            self.master_base / ".env",
            env_file,
            self.config.permissions['configs']
        )
        
        # Read and process content
        env_content = env_file.read_text()
        
        # Build replacements dictionary
        replacements = {
            "${CUSTOMER_NAME}": self.customer_name,
            "${CUSTOMER_DOMAIN}": self.customer_domain,
            "${BASE_DOMAIN}": self.config.base_domain,
            "${CONFIG_BASE}": str(self.config_base),
            "${EXTERNAL_DB_HOST}": self.config.external_db_host,
            "${EXTERNAL_DB_PORT}": self.config.external_db_port,
            "${EXTERNAL_DB_USER}": f"{self.customer_name}_admin",
            "${EXTERNAL_NEXTCLOUD_DB}": f"{self.customer_name}_nextcloud",
            "${EXTERNAL_KEYCLOAK_DB}": f"{self.customer_name}_keycloak",
            "${EXTERNAL_LLDAP_DB}": f"{self.customer_name}_lldap",
            "${CUSTOMER_IP}": self.customer_ip,
            "${REDIS_DB_INDEX}": self.redis_db_index,
        }
        
        # Add all secrets to replacements
        for key, value in self.secrets.items():
            replacements[f"${{{key.upper()}}}"] = value
        
        # Replace variables
        env_content = self.replace_variables(env_content, replacements)
        
        # Write updated content
        env_file.write_text(env_content)
    
    def setup_services_parallel(self):
        """Setup services in parallel for better performance"""
        self.logger.info("Setting up services in parallel...")
        
        # Create common SSL directory first
        ssl_dir = self.config_base / "ssl"
        self.ensure_directory(ssl_dir)
        self.setup_ssl(ssl_dir)
        
        # Services that can be set up in parallel
        service_methods = [
            ("nextcloud", self.setup_nextcloud),
            ("keycloak", self.setup_keycloak),
            ("lldap", self.setup_lldap),
            ("dovecot", self.setup_dovecot),
            ("postfix", self.setup_postfix),
            ("redis", self.setup_redis),
            ("database", self.setup_database)
        ]
        
        if self.dry_run:
            for service_name, _ in service_methods:
                self.logger.info(f"DRY RUN: Would setup {service_name}")
            return
        
        # Execute services setup in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            future_to_service = {
                executor.submit(method): service_name
                for service_name, method in service_methods
            }
            
            for future in concurrent.futures.as_completed(future_to_service):
                service_name = future_to_service[future]
                try:
                    future.result()
                    self.logger.info(f"✓ {service_name} setup completed")
                except Exception as e:
                    self.logger.error(f"✗ {service_name} setup failed: {e}")
                    raise TenantSetupError(f"Service setup failed for {service_name}: {e}")
    
    def setup_ssl(self, ssl_dir: Path):
        """Set up SSL certificates"""
        self.logger.info("Setting up SSL certificates...")
        
        if self.dry_run:
            self.logger.info("DRY RUN: Would setup SSL certificates")
            return
        
        # Copy SSL certificates
        for file_path in self.ssl_base.glob("*"):
            if file_path.is_file():
                self.copy_and_set_permissions(
                    file_path,
                    ssl_dir / file_path.name,
                    self.config.permissions['configs']
                )
    
    def setup_nextcloud(self):
        """Set up Nextcloud configuration"""
        self.logger.info("Setting up Nextcloud configuration...")
        
        nextcloud_setup_src = self.master_base / "nextcloud" / "50-setup-nc.sh"
        nextcloud_setup_dst = self.config_base / "nextcloud" / "setup" / "50-setup-nc.sh"
        
        if not nextcloud_setup_src.exists():
            self.logger.warning(f"Nextcloud setup script {nextcloud_setup_src} not found")
            return
        
        if self.dry_run:
            self.logger.info("DRY RUN: Would setup Nextcloud")
            return
        
        # Read and process content
        content = nextcloud_setup_src.read_text()
        
        replacements = {
            "${REDIS_DB_INDEX}": self.redis_db_index,
            "${REDIS_SECRET}": self.secrets['redis_secret'],
            "${SIGNAL_SECRET}": self.secrets['signal_secret'],
            "${CUSTOMER_NAME}": self.customer_name,
            "${CUSTOMER_DOMAIN}": self.customer_domain,
            "${BASE_DOMAIN}": self.config.base_domain,
            "${KEYCLOAK_REALM}": self.keycloak_realm,
            "${NEXTCLOUD_KEYCLOAK_CLIENT_SECRET}": self.secrets['nextcloud_keycloak_client_secret'],
            "${LEOS360PORTAL_KEYCLOAK_CLIENT_SECRET}": self.secrets['leos360portal_keycloak_client_secret']
        }
        
        content = self.replace_variables(content, replacements)
        
        # Write and set permissions
        nextcloud_setup_dst.write_text(content)
        os.chmod(nextcloud_setup_dst, self.config.permissions['scripts'])
    
    def setup_keycloak(self):
        """Set up Keycloak configuration"""
        self.logger.info("Setting up Keycloak configuration...")
        
        if self.dry_run:
            self.logger.info("DRY RUN: Would setup Keycloak")
            return
        
        # Copy Keycloak configs
        self.copy_and_set_permissions(
            self.master_base / "keycloak" / "start-script.sh",
            self.config_base / "keycloak" / "start-script.sh",
            self.config.permissions['scripts']
        )
        
        self.copy_and_set_permissions(
            self.master_base / "keycloak" / "dockerfile",
            self.config_base / "keycloak" / "dockerfile",
            self.config.permissions['regular']
        )
    
    def setup_lldap(self):
        """Set up LLDAP configuration"""
        self.logger.info("Setting up LLDAP configuration...")
        
        if self.dry_run:
            self.logger.info("DRY RUN: Would setup LLDAP")
            return
        
        lldap_bootstrap_dir = self.config_base / "lldap" / "config" / "bootstrap"
        
        # Files to copy with processing
        files_to_copy = [
            ("group-configs/groups.json", "group-configs/groups.json"),
            ("group-schemas/group-schemas.json", "group-schemas/group-schemas.json"),
            ("user-configs/users.json", "user-configs/users.json"),
            ("user-schemas/custom-attributes.json", "user-schemas/custom-attributes.json")
        ]
        
        for src_rel, dst_rel in files_to_copy:
            source = self.master_base / "lldap" / "bootstrap" / src_rel
            target = lldap_bootstrap_dir / dst_rel
            
            if source.exists():
                self.copy_and_set_permissions(source, target)
        
        # Process user configuration with variable replacement
        user_config_file = lldap_bootstrap_dir / "user-configs" / "users.json"
        
        if user_config_file.exists():
            content = user_config_file.read_text()
            
            replacements = {
                "${CUSTOMER_NAME}": self.customer_name,
                "${LLDAP_ADMIN_PASS}": self.secrets['lldap_admin_pass'],
                "${LLDAP_RO_PASS}": self.secrets['lldap_ro_pass'],
                "${CUSTOMER_IP}": self.customer_ip
            }
            
            content = self.replace_variables(content, replacements)
            user_config_file.write_text(content)
    
    def setup_dovecot(self):
        """Set up Dovecot configuration"""
        self.logger.info("Setting up Dovecot configuration...")
        
        if self.dry_run:
            self.logger.info("DRY RUN: Would setup Dovecot")
            return
        
        # Copy main configuration files
        self.copy_and_set_permissions(
            self.master_base / "dovecot" / "dovecot.conf",
            self.config_base / "dovecot" / "config" / "dovecot.conf"
        )
        
        self.copy_and_set_permissions(
            self.master_base / "dovecot" / "scripts" / "quota-warning.sh",
            self.config_base / "dovecot" / "config" / "scripts" / "quota-warning.sh",
            self.config.permissions['scripts']
        )
        
        # Copy conf.d files
        conf_d_source = self.master_base / "dovecot" / "conf.d"
        if conf_d_source.exists():
            for file_path in conf_d_source.glob("*"):
                if file_path.is_file():
                    self.copy_and_set_permissions(
                        file_path,
                        self.config_base / "dovecot" / "config" / "conf.d" / file_path.name
                    )
        
        # Copy SSL files
        ssl_source = self.master_base / "dovecot" / "ssl" / "dh.pem"
        if ssl_source.exists():
            self.copy_and_set_permissions(
                ssl_source,
                self.config_base / "dovecot" / "config" / "ssl" / "dh.pem"
            )
        
        # Process LDAP configuration files
        self._process_dovecot_ldap_configs()
    
    def _process_dovecot_ldap_configs(self):
        """Process Dovecot LDAP configuration files"""
        config_files = [
            ("dovecot-ldap-passdb.conf", "ldap"),
            ("dovecot-ldap-userdb.conf", "ldap")
        ]
        
        for file_name, subdir in config_files:
            source_file = self.master_base / "dovecot" / file_name
            target_file = self.config_base / "dovecot" / "config" / subdir / file_name
            
            if not source_file.exists():
                self.logger.warning(f"Dovecot source file {source_file} not found")
                continue
            
            # Read and process content
            content = source_file.read_text()
            
            replacements = {
                "${CUSTOMER_NAME}": self.customer_name,
                "${LLDAP_RO_PASS}": self.secrets['lldap_ro_pass'],
                "${CUSTOMER_IP}": self.customer_ip
            }
            
            content = self.replace_variables(content, replacements)
            
            # Write processed content
            target_file.parent.mkdir(parents=True, exist_ok=True)
            target_file.write_text(content)
            os.chmod(target_file, self.config.permissions['configs'])
    
    def setup_postfix(self):
        """Set up Postfix configuration"""
        self.logger.info("Setting up Postfix configuration...")
        
        if self.dry_run:
            self.logger.info("DRY RUN: Would setup Postfix")
            return
        
        # Copy main configuration
        self.copy_and_set_permissions(
            self.master_base / "postfix" / "main.cf",
            self.config_base / "postfix" / "config" / "main.cf.d" / "main.cf"
        )
        
        # Process LDAP configuration files
        self._process_postfix_ldap_configs()
    
    def _process_postfix_ldap_configs(self):
        """Process Postfix LDAP configuration files"""
        config_files = ["virtual_domains.cf", "virtual_aliases.cf"]
        
        for file_name in config_files:
            source_file = self.master_base / "postfix" / "ldap" / file_name
            target_file = self.config_base / "postfix" / "config" / "ldap" / file_name
            
            if not source_file.exists():
                self.logger.warning(f"Postfix source file {source_file} not found")
                continue
            
            # Read and process content
            content = source_file.read_text()
            
            replacements = {
                "${CUSTOMER_NAME}": self.customer_name,
                "${LLDAP_RO_PASS}": self.secrets['lldap_ro_pass'],
                "${CUSTOMER_IP}": self.customer_ip
            }
            
            content = self.replace_variables(content, replacements)
            
            # Write processed content
            target_file.parent.mkdir(parents=True, exist_ok=True)
            target_file.write_text(content)
            os.chmod(target_file, self.config.permissions['configs'])
    
    def setup_database(self):
        """Set up database configuration"""
        self.logger.info("Setting up database configuration...")
        
        if self.dry_run:
            self.logger.info("DRY RUN: Would setup database")
            return
        
        db_setup_file = self.config_base / "db" / f"db_setup_{self.customer_name}.sql"
        template_file = self.master_base / "db" / "db_setup.sql.template"
        
        if not template_file.exists():
            self.logger.warning(f"Database template file {template_file} not found")
            return
        
        # Read and process content
        sql_content = template_file.read_text()
        
        replacements = {
            "${DB_ADMIN_USER}": f"{self.customer_name}_admin",
            "${DB_NEXTCLOUD}": f"{self.customer_name}_nextcloud",
            "${DB_KEYCLOAK}": f"{self.customer_name}_keycloak",
            "${DB_LLDAP}": f"{self.customer_name}_lldap",
            "${DB_PASSWORD}": self.secrets['db_password'],
            "${CUSTOMER_IP}": self.customer_ip,
        }
        
        sql_content = self.replace_variables(sql_content, replacements)
        
        # Write processed content
        db_setup_file.write_text(sql_content)
        os.chmod(db_setup_file, self.config.permissions['configs'])
    
    def setup_redis(self):
        """Set up Redis configuration"""
        self.logger.info("Setting up Redis configuration...")
        
        if self.dry_run:
            self.logger.info("DRY RUN: Would setup Redis")
            return
        
        # Ensure Redis data directory exists
        redis_data_dir = self.config_base / "redis" / "data"
        self.ensure_directory(redis_data_dir)
    
    def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check"""
        self.logger.info("Performing health check...")
        
        checks = {
            'directory_structure': self._check_directory_structure(),
            'file_permissions': self._check_file_permissions(),
            'template_processing': self._check_template_processing(),
            'secrets_security': self._check_secrets_security()
        }
        
        # Overall health status
        all_passed = all(
            result.get('status') == 'OK' if isinstance(result, dict) else result
            for result in checks.values()
        )
        
        checks['overall_status'] = 'HEALTHY' if all_passed else 'ISSUES_FOUND'
        
        return checks
    
    def _check_directory_structure(self) -> Dict[str, Any]:
        """Check if directory structure is correct"""
        required_dirs = [
            self.config_base / ".env",
            self.config_base / ".secrets",
            *self.service_paths.values()
        ]
        
        missing = [str(d) for d in required_dirs if not d.exists()]
        
        return {
            'status': 'OK' if not missing else 'ERROR',
            'missing_directories': missing
        }
    
    def _check_file_permissions(self) -> Dict[str, Any]:
        """Check file permissions"""
        issues = []
        
        # Check secrets directory permissions
        secrets_dir = self.config_base / ".secrets"
        if secrets_dir.exists():
            actual_perms = oct(secrets_dir.stat().st_mode)[-3:]
            expected_perms = oct(self.config.permissions['secrets'])[-3:]
            if actual_perms != expected_perms:
                issues.append(f"Secrets directory permissions: expected {expected_perms}, got {actual_perms}")
        
        return {
            'status': 'OK' if not issues else 'WARNING',
            'issues': issues
        }
    
    def _check_template_processing(self) -> Dict[str, Any]:
        """Check if template processing was successful"""
        env_file = self.config_base / ".env"
        if not env_file.exists():
            return {'status': 'ERROR', 'message': '.env file not found'}
        
        content = env_file.read_text()
        unresolved_vars = re.findall(r'\$\{[^}]+\}', content)
        
        return {
            'status': 'OK' if not unresolved_vars else 'WARNING',
            'unresolved_variables': unresolved_vars
        }
    
    def _check_secrets_security(self) -> Dict[str, Any]:
        """Check secrets security"""
        credentials_file = self.config_base / ".secrets" / "credentials.txt"
        if not credentials_file.exists():
            return {'status': 'ERROR', 'message': 'Credentials file not found'}
        
        # Check file permissions
        perms = oct(credentials_file.stat().st_mode)[-3:]
        expected_perms = oct(self.config.permissions['secrets'])[-3:]
        
        return {
            'status': 'OK' if perms == expected_perms else 'WARNING',
            'permissions': f"expected {expected_perms}, got {perms}"
        }
    
    def verify_setup(self):
        """Enhanced setup verification"""
        self.logger.info("Verifying setup...")
        
        if self.dry_run:
            self.logger.info("DRY RUN: Would verify setup")
            return
        
        # Perform health check
        health_results = self.health_check()
        
        # Log health check results
        for check_name, result in health_results.items():
            if isinstance(result, dict):
                status = result.get('status', 'UNKNOWN')
                self.logger.info(f"Health check {check_name}: {status}")
                if status != 'OK' and 'issues' in result:
                    for issue in result['issues']:
                        self.logger.warning(f"  - {issue}")
            else:
                self.logger.info(f"Health check {check_name}: {result}")
        
        if health_results['overall_status'] != 'HEALTHY':
            self.logger.warning("Setup verification found issues, but continuing...")
        else:
            self.logger.info(f"✓ Setup verification passed for {self.customer_name}")
    
    def rollback(self):
        """Execute rollback actions in reverse order"""
        self.logger.info("Executing rollback...")
        
        for action, args, kwargs in reversed(self.rollback_actions):
            try:
                action(*args, **kwargs)
                self.logger.info(f"Rollback action completed: {action.__name__}")
            except Exception as e:
                self.logger.warning(f"Rollback action failed: {action.__name__}: {e}")
        
        self.rollback_actions.clear()
    
    def cleanup(self):
        """Enhanced cleanup with rollback"""
        self.logger.info("Cleaning up due to error...")
        self.rollback()
        
        # Final cleanup of main directory
        if self.config_base.exists() and not self.dry_run:
            try:
                shutil.rmtree(self.config_base, ignore_errors=True)
                self.logger.info(f"Removed {self.config_base}")
            except Exception as e:
                self.logger.error(f"Failed to remove {self.config_base}: {e}")
    
    def run(self) -> bool:
        """Enhanced main execution with progress tracking"""
        self.logger.info(f"Starting enhanced tenant setup for {self.customer_name}...")
        
        if self.dry_run:
            self.logger.info("=== DRY RUN MODE - NO CHANGES WILL BE MADE ===")
        
        steps = [
            ("Prerequisites Check", self.check_prerequisites),
            ("IP Allocation", self.setup_nextfreeip_optimized),
            ("Password Generation", self.generate_passwords),
            ("Directory Creation", self.create_directory_structure),
            ("Environment File", self.create_env_file),
            ("Services Setup", self.setup_services_parallel),
            ("Docker Compose", self._copy_docker_compose),
            ("Setup Verification", self.verify_setup)
        ]
        
        try:
            with tqdm(total=len(steps), desc="Tenant Setup Progress") as pbar:
                for step_name, step_func in steps:
                    pbar.set_description(f"Processing: {step_name}")
                    start_time = time.time()
                    
                    step_func()
                    
                    duration = time.time() - start_time
                    self.step_times[step_name] = duration
                    
                    pbar.update(1)
                    pbar.set_postfix_str(f"Last: {step_name} ({duration:.2f}s)")
            
            # Final summary
            total_time = time.time() - self.start_time
            self.logger.info(f"✓ Enhanced tenant setup completed in {total_time:.2f}s")
            
            # Performance summary
            self._log_performance_summary()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Setup failed: {str(e)}")
            if not self.dry_run:
                self.cleanup()
            return False
    
    def _copy_docker_compose(self):
        """Copy docker-compose.yml file"""
        self.logger.info("Copying docker-compose.yml...")
        
        if self.dry_run:
            self.logger.info("DRY RUN: Would copy docker-compose.yml")
            return
        
        self.copy_and_set_permissions(
            self.master_base / "docker-compose.yml",
            self.config_base / "docker-compose.yml"
        )
    
    def _log_performance_summary(self):
        """Log performance summary"""
        self.logger.info("=== Performance Summary ===")
        for step_name, duration in self.step_times.items():
            self.logger.info(f"{step_name}: {duration:.2f}s")
        
        total_time = time.time() - self.start_time
        self.logger.info(f"Total execution time: {total_time:.2f}s")


def load_config(config_path: str = None) -> Config:
    """Load configuration from file or use defaults"""
    if config_path:
        config_file = Path(config_path)
    else:
        # Look for config in common locations
        possible_configs = [
            Path("tenant_setup.yaml"),
            Path("/etc/tenant_setup.yaml"),
            Path.home() / ".config" / "tenant_setup.yaml"
        ]
        
        config_file = None
        for config_path in possible_configs:
            if config_path.exists():
                config_file = config_path
                break
    
    if config_file and config_file.exists():
        return Config.from_file(config_file)
    else:
        return Config()


def main():
    """Enhanced main function with argument parsing"""
    parser = argparse.ArgumentParser(
        description='Enhanced Multi-Tenant Configuration Setup',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s acme-corp                    # Basic setup
  %(prog)s acme-corp --dry-run          # Test run without changes
  %(prog)s acme-corp --config custom.yaml  # Use custom config
  %(prog)s acme-corp --log-level DEBUG  # Verbose logging
        """
    )
    
    parser.add_argument('customer_name', help='Name of the customer (lowercase, alphanumeric, hyphens only)')
    parser.add_argument('--dry-run', action='store_true', help='Perform dry run without making changes')
    parser.add_argument('--config', help='Path to configuration file')
    parser.add_argument('--log-level', default='INFO', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Set logging level')
    parser.add_argument('--version', action='version', version='Enhanced Tenant Setup v2.0')
    
    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)
    
    args = parser.parse_args()
    
    try:
        # Load configuration
        config = load_config(args.config)
        
        # Create and run setup
        setup = EnhancedTenantConfigSetup(
            customer_name=args.customer_name,
            config=config,
            dry_run=args.dry_run
        )
        
        success = setup.run()
        
        if not success:
            sys.exit(1)
        
        print(f"\n✓ Tenant setup completed successfully for '{args.customer_name}'")
        if args.dry_run:
            print("Note: This was a dry run - no actual changes were made")
        
    except KeyboardInterrupt:
        print("\n\nSetup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\nFatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()