# Snake AI
# Snake Game Reinforcement Learning Agent

## Projektübersicht

Dieses Projekt implementiert eine Snake Game Umgebung speziell für Reinforcement Learning Experimente. Das Spiel läuft vollständig in der Kommandozeile (CLI) ohne grafische Benutzeroberfläche, um maximale Performance und schnelle Trainingszyklen zu ermöglichen.

## Spielumgebung

### Spielfeld
- **Größe**: 10x10 Gitter
- **Grund**: Reduzierte Größe für beschleunigte Lernphasen und weniger Komplexität
- **Darstellung**: ASCII-basierte CLI-Ausgabe

### Spielmechanik
- **Bewegung**: 3 mögliche Aktionen während der Bewegung
  - Links drehen (relativ zur aktuellen Richtung)
  - <PERSON><PERSON><PERSON> drehen (relativ zur aktuellen Richtung)  
  - Geradeaus weiterbewegen
- **Ziel**: Futter sammeln ohne mit Wänden oder dem eigenen Körper zu kollidieren

## Reinforcement Learning Setup

### State Space (Zustandsraum)
- **Spielfeld-Matrix**: 10x10 Grid mit verschiedenen Zuständen pro Zelle
- **Snake-Position**: Koordinaten aller Körperteile
- **Futter-Position**: Aktuelle Position des Futters
- **Bewegungsrichtung**: Aktuelle Richtung der Schlange
- **Relative Position**: Distanz und Richtung zum Futter

### Action Space (Aktionsraum)
3 diskrete Aktionen:
1. **Links drehen** - Ändert Richtung um 90° nach links
2. **Rechts drehen** - Ändert Richtung um 90° nach rechts
3. **Geradeaus** - Behält aktuelle Richtung bei

### Reward System (Belohnungssystem)

#### Positive Belohnungen
- **+10 Punkte**: Erfolgreiches Sammeln von Futter
- **+1 Punkt**: Annäherung an das Futter (Distanzverringerung)
- **+50 Punkte**: Spiel erfolgreich gewonnen (gesamtes Feld gefüllt)

#### Negative Belohnungen
- **-10 Punkte**: Kollision mit Wand oder eigenem Körper (Game Over)
- **-1 Punkt**: Entfernung vom Futter (Distanzvergrößerung)
- **-0.1 Punkte**: Jeder Zeitschritt ohne Fortschritt (Effizienz-Anreiz)

#### Zusätzliche Mechanismen
- **Zeitlimit**: Verhindert endlose Schleifen
- **Survival Bonus**: Kleine Belohnung für längeres Überleben

## Technische Implementierung

### Anforderungen
- **Python 3.8+**
- **NumPy**: Für effiziente Array-Operationen
- **CLI-only**: Keine GUI-Abhängigkeiten für maximale Performance

### Performance-Optimierungen
- Reduzierte Spielfeldgröße (10x10) für schnellere Episoden
- Minimal-CLI ohne grafische Effekte
- Effiziente State-Representation
- Schnelle Reward-Berechnung

## Training-Charakteristika

### Episodenlänge
- **Durchschnitt**: 50-200 Schritte pro Episode
- **Maximum**: Begrenzt durch Zeitlimit zur Vermeidung von Endlosschleifen

### Lernherausforderungen
- **Exploration vs Exploitation**: Balance zwischen Futtersuche und sicherer Bewegung
- **Temporales Lernen**: Langfristige Strategie vs. sofortige Belohnungen
- **Räumliches Verständnis**: Navigation in begrenztem Raum
- **Selbst-Kollision**: Vermeidung des eigenen Körpers

## Use Cases

### Reinforcement Learning Algorithmen
- **Q-Learning**: Diskrete Aktionen und States
- **Deep Q-Networks (DQN)**: Neural Network basierte Approximation
- **Policy Gradient**: Direkte Policy-Optimierung
- **Actor-Critic**: Kombination aus Value und Policy Learning

### Experimentelle Anwendungen
- **Hyperparameter-Tuning**: Schnelle Evaluation verschiedener Konfigurationen
- **Algorithmus-Vergleich**: Benchmarking verschiedener RL-Methoden
- **Curriculum Learning**: Stufenweises Erhöhen der Komplexität
- **Multi-Agent**: Mehrere Agents in separaten oder geteilten Umgebungen

## Metriken und Evaluation

### Performance-Indikatoren
- **Score**: Anzahl gesammelter Futter-Einheiten
- **Episode Length**: Anzahl Schritte bis Game Over
- **Survival Rate**: Prozentsatz erfolgreich abgeschlossener Episoden
- **Learning Curve**: Verbesserung über Trainingsepisoden
- **Average Reward**: Durchschnittliche Belohnung pro Episode

### Erfolgs-Kriterien
- Konsistentes Erreichen von Scores über 5 (50% des Spielfelds)
- Stabile Learning Curve ohne starke Oszillationen
- Effizienz-Verbesserung (weniger Schritte für gleichen Score)

## Projektstruktur

```
snake-rl/
├── snake_game.py          # Hauptspiel-Implementation
├── agent.py               # RL-Agent Implementation
├── training.py            # Training-Loop und Hyperparameter
├── evaluation.py          # Testing und Metriken
├── utils.py              # Hilfsfunktionen
└── agent.md              # Diese Dokumentation
```

## Nächste Schritte

1. **Basis-Implementation**: Snake Game Environment erstellen
2. **Agent-Integration**: RL-Agent Interface definieren
3. **Training-Pipeline**: Automated Training mit Logging
4. **Hyperparameter-Tuning**: Optimale Konfiguration finden
5. **Evaluation**: Comprehensive Testing und Benchmarking
6. **Dokumentation**: Detaillierte Ergebnisse und Erkenntnisse

## Erwartete Lernerfahrungen

- Verständnis für RL-Environment Design
- Praktische Erfahrung mit Reward Engineering
- Optimierung von Training-Performance
- Debugging von RL-Algorithmen
- Evaluation von Agent-PerformanceErs