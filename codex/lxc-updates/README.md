# Advanced LXC Container Updater v2.0
## 🚀 Installations- und Feature-Guide

### 📦 Schnellinstallation

```bash
# 1. <PERSON><PERSON> herunt<PERSON>laden und ausführbar machen
chmod +x lxc-updater.sh

# 2. Erstes Update durchführen
lxc-update --dry-run  # Testlauf
lxc-update            # Echtes Update
```

### 🎯 Neue Features im Überblick

#### ⚡ Performance & Effizienz
- **Parallele Updates**: Bis zu 8 Container gleichzeitig
- **Smart Caching**: Container-Infos werden nur einmal geladen
- **Optimierte Fehlerbehandlung**: Ein Fehler stoppt nicht den ganzen Prozess
- **Fortschrittsanzeige**: Echzeit-Status mit Progressbar

#### 🛠️ Konfiguration & Flexibilität
- **Konfigurationsdatei**: `/etc/lxc-updater/config.conf`
- **Command-Line Optionen**: Über 10 verschiedene Parameter
- **Container-Filter**: Nach ID, Namen oder Mustern
- **Dry-Run Modus**: Si<PERSON><PERSON> Testlauf ohne Änderungen

#### 🔧 Erweiterte Funktionen
- **Detailliertes Logging**: Strukturierte Logs mit Rotation
- **Systemd Integration**: Timer für automatische Updates

#### 📊 Monitoring & Reports
- **Update-Statistiken**: Laufzeit, Updates, gespartes Speicher
- **Detaillierte Zusammenfassung**: Status jedes Containers
- **Reboot-Erkennung**: Automatische Identifikation nötiger Neustarts
- **Notification Support**: Slack, E-Mail (erweiterbar)

### 🚀 Verwendungsbeispiele

#### Basis-Updates
```bash
# Alle Container aktualisieren
lxc-update

# Testlauf ohne Änderungen
lxc-update --dry-run

# Verbose-Modus für Details
lxc-update --verbose
```

#### Selektive Updates
```bash
# Nur bestimmte Container
lxc-update --containers="101,102,105"

# Container nach Namen-Pattern
lxc-update --pattern="web-*"

# Mit verschiedenen Einstellungen
lxc-update --parallel=5 --skip-locale --verbose
```

#### Konfiguration
```bash
# Alternative Konfigurationsdatei
lxc-update --config=/path/to/custom.conf

# Andere Log-Datei
lxc-update --log-file=/var/log/custom.log

# Verschiedene Locales
lxc-update --locale="de_DE.UTF-8"
```


### 📋 Konfigurationsoptionen

#### Haupt-Konfiguration (`/etc/lxc-updater/config.conf`)
```bash
# Performance
MAX_PARALLEL_UPDATES=3        # Gleichzeitige Updates
UPDATE_TIMEOUT=1800           # Timeout in Sekunden

# Verhalten  
DEFAULT_LOCALE="en_US.UTF-8"  # Standard-Locale
SKIP_LOCALE_SETUP=false       # Locale-Setup überspringen
CREATE_SNAPSHOTS=true         # Auto-Snapshots vor Update

# Logging
LOG_FILE="/var/log/lxc-updater.log"
LOG_LEVEL="INFO"              # DEBUG, INFO, WARN, ERROR
KEEP_LOGS_DAYS=30            # Log-Retention

# Filter
EXCLUDE_CONTAINERS="999"      # Container ausschließen
EXCLUDE_PATTERNS="test-*"     # Pattern ausschließen

# Notifications
SEND_NOTIFICATIONS=true
SLACK_WEBHOOK="https://hooks.slack.com/..."
```

### 📊 Output-Beispiel

```
   ___      __                            __   __   _  ______
  / _ |____/ /  _____ ____  _______ ___  / /  / /  | |/_/ ___/
 / __ / _  /  / ___|/ _  |/ __/ _ / _  |/ /  / /   >  </ /__
/_/ |_\_,_/   \___/\_,_|_/  \___/\_,_/_/  /_/   /_/|_|\___/

   Advanced LXC Container Mass Updater v2.0

[INFO] 12 Container gefunden

Gefundene Container:
101: web-server (ubuntu)
102: database (debian) 
103: monitoring (ubuntu)
104: backup (debian)

Fortschritt: [████████████████████████████████████████████████] 100% (12/12)

=== Zusammenfassung ===
✓ Erfolgreich aktualisiert (10):
  - 101 (web-server)
  - 102 (database)
  ...

⊘ Übersprungen (1):
  - 999 (alpine-test - alpine)

🔄 Neustart erforderlich (2):
  - 101 (web-server) 
  - 104 (backup)

=== Update-Statistiken ===
Gesamte Laufzeit: 185s
Durchschnitt pro Container: 15s
Parallele Jobs: 3
Gesamte Updates: 47
Freigegebener Speicher: 234MB
```

### 🆚 Verbesserungen vs. Original

| Feature | Original | Advanced v2.0 |
|---------|----------|---------------|
| **Parallele Updates** | ❌ Sequenziell | ✅ Bis zu 8 parallel |
| **Fortschrittsanzeige** | ❌ Keine | ✅ Echzeit-Progressbar |
| **Container-Filter** | ❌ Alle oder nichts | ✅ ID, Namen, Pattern |
| **Konfiguration** | ❌ Hardcoded | ✅ Datei + CLI-Optionen |
| **Fehlerbehandlung** | ❌ Bricht ab | ✅ Sammelt und fortsetzt |
| **Hooks** | ❌ Keine | ✅ Pre/Post-Update Scripts |
| **Logging** | ❌ Basic | ✅ Strukturiert + Rotation |
| **Statistiken** | ❌ Minimal | ✅ Detailliert + Performance |
| **Dry-Run** | ❌ Keine | ✅ Sicherer Testmodus |
| **Automatisierung** | ❌ Manual | ✅ Systemd Timer |

### 🔧 Troubleshooting

#### Häufige Probleme
```bash
# Berechtigungsfehler
sudo chown root:root /usr/local/bin/lxc-updater.sh
sudo chmod +x /usr/local/bin/lxc-updater.sh

# Log-Verzeichnis Probleme  
sudo mkdir -p /var/log
sudo chown root:root /var/log/lxc-updater.log

# Container nicht erreichbar
pct list | grep running  # Prüfe Status
pct enter 101            # Teste manuellen Zugriff
```

#### Debug-Modus
```bash
# Maximale Details
lxc-update --verbose --log-file=/tmp/debug.log

# Nur ein Container zum Testen
lxc-update --containers="101" --verbose --dry-run
```

### 📈 Performance-Optimierung

#### Für viele Container (20+)
```bash
# Mehr parallele Jobs
lxc-update --parallel=6

# Locale-Setup überspringen wenn nicht nötig
lxc-update --skip-locale
```

#### Für langsame Systeme
```bash
# Konservative Einstellungen
lxc-update --parallel=2 --verbose
```

### 🎯 Best Practices

1. **Immer zuerst testen**: `--dry-run` verwenden
2. **Backups/Snapshots**: Vor produktiven Updates
3. **Monitoring**: Logs regelmäßig prüfen
4. **Selective Updates**: Nicht kritische Container zuerst
5. **Hook-System nutzen**: Für container-spezifische Aufgaben
6. **Automatisierung**: Timer für regelmäßige Updates

### 📞 Support & Erweiterungen

Das Skript ist modular aufgebaut und kann einfach erweitert werden:
- Neue Distributionen (CentOS, Alpine)
- Weitere Notification-Kanäle
- Custom Update-Strategien
- Integration mit Monitoring-Systemen