#!/usr/bin/env bash

# ===============================================================================
# Advanced LXC Container Mass Updater for Proxmox VE - FIXED VERSION
# Version: 2.1 - Robuste Fehlerbehandlung
# ===============================================================================

set -euo pipefail

# Exit codes
readonly EXIT_SUCCESS=0
readonly EXIT_NO_CONTAINERS=1
readonly EXIT_PERMISSION_ERROR=2
readonly EXIT_CONFIG_ERROR=3

# Default configuration
DEFAULT_CONFIG="/etc/lxc-updater/config.conf"
DEFAULT_LOCALE="en_US.UTF-8"
MAX_PARALLEL_UPDATES=3
LOG_FILE="/var/log/lxc-updater.log"
DRY_RUN=false
SKIP_LOCALE_SETUP=false
VERBOSE=false
SELECTED_CONTAINERS=""
CONTAINER_PATTERN=""
HOOKS_DIR="/etc/lxc-updater/hooks"

# Global arrays for caching
declare -A CONTAINER_NAMES CONTAINER_OS
declare -a RUNNING_CONTAINERS=()
declare -a updated_containers=()
declare -a failed_containers=()
declare -a skipped_containers=()
declare -a reboot_containers=()

# Colors
readonly YW='\033[33m'      # Yellow
readonly BL='\033[36m'      # Blue  
readonly RD='\033[01;31m'   # Red
readonly GN='\033[1;92m'    # Green
readonly CL='\033[m'        # Clear
readonly BD='\033[1m'       # Bold

# Statistics
START_TIME=$(date +%s)
TOTAL_CONTAINERS=0
PROCESSED_CONTAINERS=0
TOTAL_UPDATES=0
TOTAL_SPACE_SAVED=0

# Trap for cleanup
trap 'cleanup_on_exit' EXIT

function cleanup_on_exit() {
    # Kill background jobs if any
    local pids
    pids=$(jobs -p 2>/dev/null || true)
    if [[ -n "$pids" ]]; then
        echo "$pids" | xargs -r kill 2>/dev/null || true
        wait 2>/dev/null || true
    fi
}

function load_config() {
    local config_file=${1:-$DEFAULT_CONFIG}
    
    if [[ -f "$config_file" ]]; then
        # Source config file safely
        source "$config_file" || log_message "WARN" "Fehler beim Laden der Konfiguration"
        log_message "INFO" "Konfiguration geladen: $config_file"
    else
        log_message "WARN" "Keine Konfigurationsdatei gefunden, verwende Standardwerte"
    fi
}

function show_help() {
    cat << 'EOF'
===============================================================================
Advanced LXC Container Mass Updater v2.1 - Fixed Version
===============================================================================

VERWENDUNG:
    ./lxc-updater.sh [OPTIONEN]

OPTIONEN:
    -h, --help              Zeige diese Hilfe
    -d, --dry-run          Simuliere Updates ohne Ausführung
    -v, --verbose          Detaillierte Ausgabe
    -c, --config FILE      Alternative Konfigurationsdatei
    -l, --locale LOCALE    Standard-Locale (default: en_US.UTF-8)
    -p, --parallel N       Max parallele Updates (default: 3)
    -s, --skip-locale      Überspringe Locale-Konfiguration
    --containers IDS       Update nur spezifische Container (comma-separated)
    --pattern PATTERN      Update Container nach Namensmuster
    --log-file FILE        Alternative Log-Datei

BEISPIELE:
    lxc-updater.sh                              # Update alle Container
    lxc-updater.sh --dry-run                    # Simulation
    lxc-updater.sh --containers="101,102,105"   # Nur bestimmte Container
    lxc-updater.sh --parallel=5 --verbose       # 5 parallele Updates mit Details

===============================================================================
EOF
}

function parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit $EXIT_SUCCESS
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -c|--config)
                load_config "$2"
                shift 2
                ;;
            -l|--locale)
                DEFAULT_LOCALE="$2"
                shift 2
                ;;
            -p|--parallel)
                MAX_PARALLEL_UPDATES="$2"
                shift 2
                ;;
            -s|--skip-locale)
                SKIP_LOCALE_SETUP=true
                shift
                ;;
            --containers)
                SELECTED_CONTAINERS="$2"
                shift 2
                ;;
            --pattern)
                CONTAINER_PATTERN="$2"
                shift 2
                ;;
            --log-file)
                LOG_FILE="$2"
                shift 2
                ;;
            *)
                echo -e "${RD}Unbekannte Option: $1${CL}"
                show_help
                exit $EXIT_CONFIG_ERROR
                ;;
        esac
    done
}

function log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # Create log directory if it doesn't exist
    mkdir -p "$(dirname "$LOG_FILE")" 2>/dev/null || true
    
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE" 2>/dev/null || true
    
    # Console output with colors
    case $level in
        "ERROR") echo -e "${RD}[ERROR]${CL} $message" ;;
        "WARN")  echo -e "${YW}[WARN]${CL} $message" ;;
        "INFO")  echo -e "${BL}[INFO]${CL} $message" ;;
        "SUCCESS") echo -e "${GN}[SUCCESS]${CL} $message" ;;
        "DEBUG") 
            if [[ "$VERBOSE" == true ]]; then
                echo -e "${BL}[DEBUG]${CL} $message"
            fi
            ;;
        *) echo "[$level] $message" ;;
    esac
}

function header_info() {
    clear
    cat <<"EOF"
   ___      __                            __   __   _  ______
  / _ |____/ /  _____ ____  _______ ___  / /  / /  | |/_/ ___/
 / __ / _  /  / ___|/ _  |/ __/ _ / _  |/ /  / /   >  </ /__
/_/ |_\_,_/   \___/\_,_|_/  \___/\_,_/_/  /_/   /_/|_|\___/

   Advanced LXC Container Mass Updater v2.1
EOF
    echo ""
}

function validate_prerequisites() {
    # Check if running as root
    if [[ $EUID -ne 0 ]]; then
        log_message "ERROR" "Dieses Skript muss als root ausgeführt werden"
        exit $EXIT_PERMISSION_ERROR
    fi
    
    # Check if pct command exists
    if ! command -v pct >/dev/null 2>&1; then
        log_message "ERROR" "pct Kommando nicht gefunden. Läuft auf Proxmox VE?"
        exit $EXIT_CONFIG_ERROR
    fi
    
    log_message "DEBUG" "Alle Voraussetzungen erfüllt"
}

function show_progress() {
    local current=$1
    local total=$2
    local container_name=${3:-""}
    
    if [[ $total -eq 0 ]]; then
        return
    fi
    
    local percent=$((current * 100 / total))
    local filled=$((percent / 2))
    
    printf "\r${BL}Fortschritt:${CL} ["
    printf "%*s" $filled 2>/dev/null | tr ' ' '█' || true
    printf "%*s" $((50 - filled)) 2>/dev/null | tr ' ' '░' || true
    printf "] %3d%% (%d/%d) %s${CL}" $percent $current $total "$container_name"
    
    if [ $current -eq $total ]; then
        echo ""
    fi
}

function discover_containers() {
    log_message "INFO" "Suche laufende Container..."
    
    local all_running
    all_running=$(pct list 2>/dev/null | grep "running" | awk '{print $1}' 2>/dev/null || true)
    
    if [[ -z "$all_running" ]]; then
        log_message "ERROR" "Keine laufenden Container gefunden!"
        exit $EXIT_NO_CONTAINERS
    fi
    
    # Reset arrays
    RUNNING_CONTAINERS=()
    
    # Apply filters
    local container_count=0
    for container in $all_running; do
        local should_include=true
        
        # Filter by specific container IDs
        if [[ -n "$SELECTED_CONTAINERS" ]]; then
            should_include=false
            IFS=',' read -ra CONTAINER_IDS <<< "$SELECTED_CONTAINERS"
            for id in "${CONTAINER_IDS[@]}"; do
                if [[ "$container" == "$id" ]]; then
                    should_include=true
                    break
                fi
            done
        fi
        
        if [[ "$should_include" == true ]]; then
            RUNNING_CONTAINERS+=("$container")
            container_count=$((container_count + 1))
        fi
    done
    
    TOTAL_CONTAINERS=${#RUNNING_CONTAINERS[@]}
    
    if [[ $TOTAL_CONTAINERS -eq 0 ]]; then
        log_message "ERROR" "Keine Container entsprechen den Filterkriterien!"
        exit $EXIT_NO_CONTAINERS
    fi
    
    log_message "SUCCESS" "$TOTAL_CONTAINERS Container gefunden"
    log_message "DEBUG" "Container IDs: ${RUNNING_CONTAINERS[*]}"
}

function safe_get_container_name() {
    local container=$1
    local name
    
    if name=$(pct exec "$container" hostname 2>/dev/null); then
        echo "$name"
    else
        echo "container-$container"
    fi
}

function safe_get_container_os() {
    local container=$1
    local ostype
    
    if ostype=$(pct config "$container" 2>/dev/null | awk '/^ostype/ {print $2}'); then
        echo "$ostype"
    else
        echo "unknown"
    fi
}

function cache_container_info() {
    log_message "INFO" "Lade Container-Informationen..."
    
    if [[ ${#RUNNING_CONTAINERS[@]} -eq 0 ]]; then
        log_message "ERROR" "Keine Container zum Laden von Informationen gefunden"
        return 1
    fi
    
    local count=0
    local total=${#RUNNING_CONTAINERS[@]}
    
    for container in "${RUNNING_CONTAINERS[@]}"; do
        count=$((count + 1))
        show_progress $count $total "Container $container"
        
        # Validate container ID
        if ! [[ "$container" =~ ^[0-9]+$ ]]; then
            log_message "WARN" "Überspringe ungültige Container-ID: $container"
            continue
        fi
        
        # Cache hostname safely
        local hostname
        hostname=$(safe_get_container_name "$container")
        CONTAINER_NAMES[$container]="$hostname"
        
        # Cache OS type safely
        local ostype
        ostype=$(safe_get_container_os "$container")
        CONTAINER_OS[$container]="$ostype"
        
        # Apply pattern filter if specified
        if [[ -n "$CONTAINER_PATTERN" ]]; then
            if [[ ! "$hostname" == $CONTAINER_PATTERN ]]; then
                log_message "DEBUG" "Container $container ($hostname) entspricht nicht Pattern $CONTAINER_PATTERN"
                continue
            fi
        fi
        
        log_message "DEBUG" "Container $container: $hostname ($ostype)"
    done
    
    echo ""
    log_message "SUCCESS" "Container-Informationen geladen"
}

function display_container_list() {
    echo -e "\n${GN}Gefundene Container:${CL}"
    for container in "${RUNNING_CONTAINERS[@]}"; do
        local name="${CONTAINER_NAMES[$container]:-unknown}"
        local os="${CONTAINER_OS[$container]:-unknown}"
        echo -e "${BL}$container${CL}: ${GN}$name${CL} (${YW}$os${CL})"
    done
    echo ""
}

function run_hook() {
    local hook_type=$1
    local container=$2
    local hook_file="$HOOKS_DIR/${hook_type}-${container}.sh"
    
    if [[ -f "$hook_file" && -x "$hook_file" ]]; then
        log_message "DEBUG" "Führe $hook_type Hook für Container $container aus"
        if [[ "$DRY_RUN" == false ]]; then
            if ! bash "$hook_file" "$container" 2>/dev/null; then
                log_message "WARN" "$hook_type Hook fehlgeschlagen für Container $container"
            fi
        fi
    fi
}

function setup_locale() {
    local container=$1
    
    if [[ "$SKIP_LOCALE_SETUP" == true ]]; then
        return 0
    fi
    
    log_message "DEBUG" "Prüfe Locale für Container $container"
    
    # Check if locale is already configured
    if pct exec "$container" -- bash -c "locale -a 2>/dev/null | grep -q '${DEFAULT_LOCALE}'" 2>/dev/null; then
        log_message "DEBUG" "Locale bereits konfiguriert"
        return 0
    fi
    
    if [[ "$DRY_RUN" == true ]]; then
        echo "  [DRY-RUN] Würde Locale $DEFAULT_LOCALE konfigurieren"
        return 0
    fi
    
    log_message "DEBUG" "Konfiguriere Locale $DEFAULT_LOCALE..."
    
    # Check if apt is available
    if ! pct exec "$container" -- which apt-get >/dev/null 2>&1; then
        log_message "WARN" "apt-get nicht verfügbar in Container $container"
        return 1
    fi
    
    # Configure locale (simplified)
    if pct exec "$container" -- bash -c "
        apt-get update >/dev/null 2>&1 &&
        DEBIAN_FRONTEND=noninteractive apt-get install -y locales >/dev/null 2>&1 &&
        echo '$DEFAULT_LOCALE UTF-8' > /etc/locale.gen &&
        locale-gen >/dev/null 2>&1 &&
        update-locale LANG=$DEFAULT_LOCALE LC_ALL=$DEFAULT_LOCALE >/dev/null 2>&1
    " 2>/dev/null; then
        return 0
    else
        log_message "WARN" "Locale-Setup fehlgeschlagen für Container $container"
        return 1
    fi
}

function update_container() {
    local container=$1
    local name="${CONTAINER_NAMES[$container]:-unknown}"
    local os="${CONTAINER_OS[$container]:-unknown}"
    local failed_steps=()
    
    # Check OS type
    if [[ ! "$os" =~ ^(debian|ubuntu)$ ]]; then
        log_message "DEBUG" "Überspringe Container $container ($os) - Kein Debian/Ubuntu"
        skipped_containers+=("$container ($name - $os)")
        return 0
    fi
    
    log_message "INFO" "Update Container $container: $name ($os)"
    
    # Run pre-update hook
    run_hook "pre-update" "$container"
    
    # Setup locale
    if ! setup_locale "$container"; then
        failed_steps+=("locale")
    fi
    
    if [[ "$DRY_RUN" == true ]]; then
        echo "  [DRY-RUN] Würde Updates für Container $container durchführen"
        updated_containers+=("$container ($name)")
        return 0
    fi
    
    # Get and perform updates
    local updates_info
    updates_info=$(pct exec "$container" -- bash -c "
        apt-get -qq update 2>/dev/null
        apt-get -s upgrade 2>/dev/null | grep -P '^\d+ upgraded' || echo 'Keine Updates verfügbar'
    " 2>/dev/null || echo "Fehler beim Prüfen der Updates")
    
    echo "  Updates: $updates_info"
    
    if [[ "$updates_info" == "Keine Updates verfügbar" ]]; then
        updated_containers+=("$container ($name - keine Updates)")
    else
        # Perform actual update
        if pct exec "$container" -- bash -c "
            DEBIAN_FRONTEND=noninteractive apt-get -y dist-upgrade >/dev/null 2>&1 &&
            apt-get clean >/dev/null 2>&1 &&
            apt-get -y autoremove >/dev/null 2>&1
        " 2>/dev/null; then
            updated_containers+=("$container ($name)")
            log_message "SUCCESS" "Container $container erfolgreich aktualisiert"
        else
            failed_steps+=("updates")
            failed_containers+=("$container ($name - updates)")
            log_message "WARN" "Update fehlgeschlagen für Container $container"
        fi
    fi
    
    # Check if reboot is required
    if pct exec "$container" -- [ -e "/var/run/reboot-required" ] 2>/dev/null; then
        reboot_containers+=("$container ($name)")
    fi
    
    # Run post-update hook
    run_hook "post-update" "$container"
    
    return 0
}

function update_containers_parallel() {
    log_message "INFO" "Starte Updates (max $MAX_PARALLEL_UPDATES gleichzeitig)..."
    
    local pids=()
    local active_jobs=0
    PROCESSED_CONTAINERS=0
    
    for container in "${RUNNING_CONTAINERS[@]}"; do
        # Wait if we have reached max parallel jobs
        while [[ $active_jobs -ge $MAX_PARALLEL_UPDATES ]]; do
            # Check for completed jobs
            local new_pids=()
            for pid in "${pids[@]}"; do
                if kill -0 "$pid" 2>/dev/null; then
                    new_pids+=("$pid")
                else
                    active_jobs=$((active_jobs - 1))
                    PROCESSED_CONTAINERS=$((PROCESSED_CONTAINERS + 1))
                    show_progress $PROCESSED_CONTAINERS $TOTAL_CONTAINERS
                fi
            done
            pids=("${new_pids[@]}")
            sleep 0.2
        done
        
        # Start new update job
        (update_container "$container") &
        pids+=($!)
        active_jobs=$((active_jobs + 1))
        
        sleep 0.1
    done
    
    # Wait for all remaining jobs to complete
    for pid in "${pids[@]}"; do
        wait "$pid" 2>/dev/null || true
        PROCESSED_CONTAINERS=$((PROCESSED_CONTAINERS + 1))
        show_progress $PROCESSED_CONTAINERS $TOTAL_CONTAINERS
    done
    
    echo ""
    log_message "SUCCESS" "Alle Updates abgeschlossen"
}

function generate_summary() {
    local end_time=$(date +%s)
    local total_time=$((end_time - START_TIME))
    
    echo -e "\n${BD}=== Zusammenfassung ===${CL}"
    
    if [[ ${#updated_containers[@]} -gt 0 ]]; then
        echo -e "${GN}✓ Erfolgreich aktualisiert (${#updated_containers[@]}):${CL}"
        for container in "${updated_containers[@]}"; do
            echo "  - $container"
        done
    fi
    
    if [[ ${#skipped_containers[@]} -gt 0 ]]; then
        echo -e "${YW}⊘ Übersprungen (${#skipped_containers[@]}):${CL}"
        for container in "${skipped_containers[@]}"; do
            echo "  - $container"
        done
    fi
    
    if [[ ${#failed_containers[@]} -gt 0 ]]; then
        echo -e "${RD}✗ Fehlgeschlagen (${#failed_containers[@]}):${CL}"
        for container in "${failed_containers[@]}"; do
            echo "  - $container"
        done
    fi
    
    if [[ ${#reboot_containers[@]} -gt 0 ]]; then
        echo -e "${RD}🔄 Neustart erforderlich (${#reboot_containers[@]}):${CL}"
        for container in "${reboot_containers[@]}"; do
            echo "  - $container"
        done
        echo -e "\n${YW}Hinweis:${CL} Container können mit 'pct reboot <ID>' neugestartet werden"
    fi
    
    echo -e "\n${BD}=== Statistiken ===${CL}"
    echo -e "${BL}Gesamte Laufzeit:${CL} ${total_time}s"
    echo -e "${BL}Parallele Jobs:${CL} $MAX_PARALLEL_UPDATES"
    
    if [[ $PROCESSED_CONTAINERS -gt 0 ]]; then
        local avg_time=$((total_time / PROCESSED_CONTAINERS))
        echo -e "${BL}Durchschnitt pro Container:${CL} ${avg_time}s"
    fi
}

function main() {
    header_info
    
    # Parse command line arguments
    parse_arguments "$@"
    
    # Load configuration
    load_config
    
    # Validate prerequisites
    validate_prerequisites
    
    if [[ "$DRY_RUN" == true ]]; then
        log_message "INFO" "DRY-RUN Modus aktiviert - keine Änderungen werden durchgeführt"
    fi
    
    # Discover and cache container information
    discover_containers
    cache_container_info
    display_container_list
    
    # Ask for confirmation if not in verbose/dry-run mode
    if [[ "$VERBOSE" == false && "$DRY_RUN" == false ]]; then
        echo -ne "${YW}Fortfahren mit Update von $TOTAL_CONTAINERS Containern? [y/N]${CL} "
        read -r response
        if [[ ! "$response" =~ ^[Yy]([Ee][Ss])?$ ]]; then
            log_message "INFO" "Update abgebrochen durch Benutzer"
            exit $EXIT_SUCCESS
        fi
    fi
    
    # Start the update process
    echo ""
    update_containers_parallel
    
    # Generate final summary
    generate_summary
    
    log_message "SUCCESS" "LXC Container Update-Prozess abgeschlossen"
    
    # Exit with appropriate code
    if [[ ${#failed_containers[@]} -gt 0 ]]; then
        exit 1
    else
        exit $EXIT_SUCCESS
    fi
}

# Run main function with all arguments
main "$@"