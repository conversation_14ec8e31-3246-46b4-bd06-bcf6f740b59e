def _check_proxychains_availability(self) -> bool:
        """Prüft ob proxychains4 verfügbar ist"""
        try:
            result = subprocess.run(['proxychains4', '--help'], 
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False#!/usr/bin/env python3
"""
Oniux Security Testing Script
============================
WICHTIGER HINWEIS: Dieses Script darf nur für autorisierte Sicherheitstests 
auf eigenen Systemen oder mit expliziter schriftlicher Genehmigung verwendet werden!

Voraussetzungen:
- Linux System mit Rust toolchain
- oniux installiert: cargo install --git https://gitlab.torproject.org/tpo/core/oniux oniux@0.4.0
- Python 3.6+
"""

import subprocess
import json
import logging
import sys
from typing import List, Dict, Optional
import time
import argparse
from urllib.parse import urlparse

# Logging Setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('oniux_security_test.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class OniuxSecurityTester:
    """
    Klasse für Sicherheitstests mit oniux über Tor
    """
    
    def __init__(self, target_list: List[str], authorized: bool = False):
        """
        Initialisierung des Security Testers
        
        Args:
            target_list: Liste der zu testenden Ziele
            authorized: Bestätigung der Autorisierung
        """
        if not authorized:
            raise ValueError("FEHLER: Autorisierung erforderlich! Setzen Sie authorized=True nur bei eigenen Systemen!")
        
        self.targets = target_list
        self.results = {}
        
        # Prüfe ob oniux verfügbar ist
        if not self._check_oniux_availability():
            raise RuntimeError("oniux ist nicht verfügbar! Bitte installieren Sie es zuerst.")
    
    def _check_oniux_availability(self) -> bool:
        """Prüft ob oniux installiert und verfügbar ist"""
        try:
            result = subprocess.run(['oniux', '--help'], 
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def _run_oniux_command(self, command: List[str], timeout: int = 30, use_proxychains: bool = False) -> Dict:
        """
        Führt einen oniux Befehl aus
        
        Args:
            command: Liste der Befehlsargumente
            timeout: Timeout in Sekunden
            use_proxychains: Fallback auf proxychains4 falls oniux TUN-Probleme hat
            
        Returns:
            Dict mit Ergebnis und Metadaten
        """
        if use_proxychains and self._check_proxychains_availability():
            full_command = ['proxychains4', '-q'] + command
            logger.info(f"Verwende Proxychains Fallback: {' '.join(full_command)}")
        else:
            full_command = ['oniux'] + command
            logger.info(f"Führe aus: {' '.join(full_command)}")
        
        try:
            start_time = time.time()
            result = subprocess.run(
                full_command,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            end_time = time.time()
            
            # Bei oniux TUN-Fehler automatisch Proxychains versuchen
            if result.returncode != 0 and 'tun interface' in result.stderr and not use_proxychains:
                logger.warn("TUN-Interface Problem erkannt, versuche Proxychains...")
                return self._run_oniux_command(command, timeout, use_proxychains=True)
            
            return {
                'command': ' '.join(full_command),
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'execution_time': end_time - start_time,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'method': 'proxychains' if use_proxychains else 'oniux'
            }
            
        except subprocess.TimeoutExpired:
            logger.error(f"Timeout bei Befehl: {' '.join(full_command)}")
            return {
                'command': ' '.join(full_command),
                'returncode': -1,
                'stdout': '',
                'stderr': 'TIMEOUT',
                'execution_time': timeout,
                'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def check_target_accessibility(self, target: str) -> Dict:
        """
        Prüft die Erreichbarkeit eines Ziels
        
        Args:
            target: Ziel-URL oder IP
            
        Returns:
            Dict mit Testergebnissen
        """
        logger.info(f"Teste Erreichbarkeit von: {target}")
        
        # Basic HTTP/HTTPS Check
        if target.startswith('http'):
            result = self._run_oniux_command(['curl', '-I', '--max-time', '30', target])
        else:
            # Erst HTTPS versuchen, dann HTTP
            result = self._run_oniux_command(['curl', '-I', '--max-time', '30', f'https://{target}'])
            if result['returncode'] != 0:
                logger.info(f"HTTPS fehlgeschlagen, versuche HTTP für {target}")
                result = self._run_oniux_command(['curl', '-I', '--max-time', '30', f'http://{target}'])
        
        # Analysiere Response
        accessible = result['returncode'] == 0
        headers = self._parse_http_headers(result['stdout']) if accessible else {}
        
        return {
            'target': target,
            'accessible': accessible,
            'headers': headers,
            'response_time': result['execution_time'],
            'raw_result': result
        }
    
    def _parse_http_headers(self, curl_output: str) -> Dict:
        """Parst HTTP Headers aus curl Output"""
        headers = {}
        lines = curl_output.split('\n')
        
        for line in lines:
            if ':' in line and not line.startswith('HTTP/'):
                parts = line.split(':', 1)
                if len(parts) == 2:
                    key = parts[0].strip()
                    value = parts[1].strip()
                    headers[key] = value
        
        return headers
    
    def scan_common_ports(self, target: str, ports: List[int] = None) -> Dict:
        """
        Scannt häufige Ports eines Ziels
        
        Args:
            target: Ziel-IP oder Domain
            ports: Liste der zu scannenden Ports
            
        Returns:
            Dict mit Port-Scan Ergebnissen
        """
        if ports is None:
            ports = [21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 8080, 8443]
        
        logger.info(f"Scanne Ports auf {target}")
        
        open_ports = []
        
        for port in ports:
            # Verwende netcat über oniux für Port-Scan
            result = self._run_oniux_command([
                'sh', '-c', f'echo | nc -w 5 {target} {port}'
            ], timeout=10)
            
            if result['returncode'] == 0:
                open_ports.append(port)
                logger.info(f"Port {port} ist offen auf {target}")
        
        return {
            'target': target,
            'scanned_ports': ports,
            'open_ports': open_ports,
            'scan_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def check_security_headers(self, target: str) -> Dict:
        """
        Prüft Sicherheits-Headers einer Website
        
        Args:
            target: Ziel-URL
            
        Returns:
            Dict mit Security Header Analyse
        """
        logger.info(f"Prüfe Security Headers von: {target}")
        
        result = self._run_oniux_command(['curl', '-I', target])
        
        if result['returncode'] != 0:
            return {
                'target': target,
                'error': 'Nicht erreichbar',
                'security_score': 0
            }
        
        headers = self._parse_http_headers(result['stdout'])
        
        # Wichtige Security Headers prüfen
        security_headers = {
            'Strict-Transport-Security': headers.get('Strict-Transport-Security'),
            'Content-Security-Policy': headers.get('Content-Security-Policy'),
            'X-Frame-Options': headers.get('X-Frame-Options'),
            'X-Content-Type-Options': headers.get('X-Content-Type-Options'),
            'Referrer-Policy': headers.get('Referrer-Policy'),
            'Permissions-Policy': headers.get('Permissions-Policy')
        }
        
        # Security Score berechnen
        present_headers = sum(1 for v in security_headers.values() if v is not None)
        security_score = (present_headers / len(security_headers)) * 100
        
        return {
            'target': target,
            'security_headers': security_headers,
            'security_score': security_score,
            'recommendations': self._get_security_recommendations(security_headers)
        }
    
    def _get_security_recommendations(self, headers: Dict) -> List[str]:
        """Gibt Sicherheitsempfehlungen basierend auf fehlenden Headern"""
        recommendations = []
        
        if not headers.get('Strict-Transport-Security'):
            recommendations.append("HSTS Header hinzufügen für HTTPS-Sicherheit")
        
        if not headers.get('Content-Security-Policy'):
            recommendations.append("CSP Header implementieren gegen XSS")
        
        if not headers.get('X-Frame-Options'):
            recommendations.append("X-Frame-Options Header gegen Clickjacking")
        
        if not headers.get('X-Content-Type-Options'):
            recommendations.append("X-Content-Type-Options: nosniff Header hinzufügen")
        
        return recommendations
    
    def run_comprehensive_scan(self) -> Dict:
        """
        Führt einen umfassenden Sicherheitsscan durch
        
        Returns:
            Dict mit allen Testergebnissen
        """
        logger.info("Starte umfassenden Sicherheitsscan...")
        
        all_results = {
            'scan_start': time.strftime('%Y-%m-%d %H:%M:%S'),
            'targets': {},
            'summary': {}
        }
        
        for target in self.targets:
            logger.info(f"Scanne Ziel: {target}")
            
            target_results = {
                'accessibility': self.check_target_accessibility(target),
                'security_headers': self.check_security_headers(target) if target.startswith('http') else None,
                'port_scan': self.scan_common_ports(target.replace('http://', '').replace('https://', '').split('/')[0])
            }
            
            all_results['targets'][target] = target_results
        
        # Summary erstellen
        accessible_count = sum(1 for t in all_results['targets'].values() if t['accessibility']['accessible'])
        all_results['summary'] = {
            'total_targets': len(self.targets),
            'accessible_targets': accessible_count,
            'scan_completed': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return all_results
    
    def save_results(self, results: Dict, filename: str = None):
        """Speichert Ergebnisse in JSON-Datei"""
        if filename is None:
            filename = f"oniux_scan_results_{int(time.time())}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Ergebnisse gespeichert in: {filename}")

def main():
    parser = argparse.ArgumentParser(description='Oniux Security Testing Tool')
    parser.add_argument('targets', nargs='+', help='Ziel-URLs oder IPs zum Testen')
    parser.add_argument('--authorized', action='store_true', 
                       help='Bestätigung der Autorisierung (NUR für eigene Systeme!)')
    parser.add_argument('--output', '-o', help='Output-Datei für Ergebnisse')
    
    args = parser.parse_args()
    
    if not args.authorized:
        print("WARNUNG: Dieses Tool darf nur für autorisierte Tests verwendet werden!")
        print("Verwenden Sie --authorized nur bei eigenen/autorisierten Systemen!")
        confirmation = input("Bestätigen Sie, dass Sie berechtigt sind, diese Ziele zu testen? (ja/nein): ")
        if confirmation.lower() != 'ja':
            print("Test abgebrochen.")
            sys.exit(1)
    
    try:
        # Security Tester initialisieren
        tester = OniuxSecurityTester(args.targets, authorized=True)
        
        # Umfassenden Scan durchführen
        results = tester.run_comprehensive_scan()
        
        # Ergebnisse anzeigen
        print("\n" + "="*50)
        print("SCAN ERGEBNISSE")
        print("="*50)
        
        for target, target_results in results['targets'].items():
            print(f"\nZiel: {target}")
            print(f"  Erreichbar: {'Ja' if target_results['accessibility']['accessible'] else 'Nein'}")
            
            if target_results['security_headers']:
                score = target_results['security_headers']['security_score']
                print(f"  Security Score: {score:.1f}%")
            
            open_ports = target_results['port_scan']['open_ports']
            if open_ports:
                print(f"  Offene Ports: {', '.join(map(str, open_ports))}")
        
        # Ergebnisse speichern
        tester.save_results(results, args.output)
        
        print(f"\nScan abgeschlossen. Details in der Log-Datei und JSON-Output.")
        
    except Exception as e:
        logger.error(f"Fehler beim Scan: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()