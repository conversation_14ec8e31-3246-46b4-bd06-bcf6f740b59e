#!/bin/bash
# Rust & Oniux Setup Script
# =========================

set -e

# Farben für Output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Schritt 1: Rust-Umgebung prüfen und konfigurieren
setup_rust_environment() {
    log_info "Konfiguriere Rust-Umgebung..."
    
    # Cargo-Pfad hinzufügen falls nicht vorhanden
    if [ ! -d "$HOME/.cargo" ]; then
        log_warn "~/.cargo Verzeichnis nicht gefunden, erstelle es..."
        mkdir -p "$HOME/.cargo/bin"
    fi
    
    # PATH erweitern
    export PATH="$HOME/.cargo/bin:$PATH"
    
    # In .bashrc dauerhaft speichern
    if ! grep -q '.cargo/bin' ~/.bashrc; then
        echo 'export PATH="$HOME/.cargo/bin:$PATH"' >> ~/.bashrc
        log_info "Rust PATH zu ~/.bashrc hinzugefügt"
    fi
    
    # Source cargo env falls vorhanden
    if [ -f "$HOME/.cargo/env" ]; then
        source "$HOME/.cargo/env"
        log_info "Cargo-Umgebung geladen"
    fi
    
    # Rustup konfigurieren
    if command -v rustup &> /dev/null; then
        rustup default stable
        log_info "Standard Rust Toolchain gesetzt"
    fi
    
    # Versionen prüfen
    if command -v rustc &> /dev/null && command -v cargo &> /dev/null; then
        log_info "✅ Rust erfolgreich konfiguriert:"
        echo "   rustc: $(rustc --version)"
        echo "   cargo: $(cargo --version)"
        return 0
    else
        log_error "❌ Rust-Konfiguration fehlgeschlagen"
        return 1
    fi
}

# Schritt 2: Oniux installieren
install_oniux() {
    log_info "Installiere oniux..."
    
    # Verschiedene Installationsmethoden versuchen
    local install_methods=(
        "cargo install --git https://gitlab.torproject.org/tpo/core/oniux oniux@0.4.0"
        "cargo install --git https://gitlab.torproject.org/tpo/core/oniux"
        "cargo install --git https://gitlab.torproject.org/tpo/core/oniux.git"
    )
    
    for method in "${install_methods[@]}"; do
        log_info "Versuche: $method"
        
        if eval "$method"; then
            log_info "✅ Oniux erfolgreich installiert"
            return 0
        else
            log_warn "❌ Methode fehlgeschlagen, versuche nächste..."
        fi
    done
    
    log_error "❌ Alle Installationsmethoden fehlgeschlagen"
    return 1
}

# Schritt 3: Installation testen
test_oniux() {
    log_info "Teste oniux Installation..."
    
    # Prüfe ob oniux verfügbar ist
    if ! command -v oniux &> /dev/null; then
        log_error "❌ oniux Befehl nicht gefunden"
        log_info "Versuche manuelle PATH-Konfiguration..."
        
        # Suche oniux binary
        local oniux_path=$(find "$HOME/.cargo" -name "oniux" -type f 2>/dev/null | head -1)
        if [ -n "$oniux_path" ]; then
            log_info "Oniux gefunden unter: $oniux_path"
            export PATH="$(dirname "$oniux_path"):$PATH"
        else
            return 1
        fi
    fi
    
    # Hilfe anzeigen
    if oniux --help &> /dev/null; then
        log_info "✅ Oniux ist funktionsfähig"
        return 0
    else
        log_error "❌ Oniux funktioniert nicht korrekt"
        return 1
    fi
}

# Schritt 4: Tor-Verbindung testen
test_tor_connection() {
    log_info "Teste Tor-Verbindung..."
    
    # Einfacher IP-Check
    if oniux curl --max-time 10 -s https://icanhazip.com; then
        log_info "✅ Tor-Verbindung funktioniert"
        log_info "Ihre Tor-Exit-IP wurde angezeigt"
        return 0
    else
        log_warn "❌ Tor-Verbindung fehlgeschlagen oder langsam"
        log_info "Versuche alternativen Test..."
        
        # Alternativer Test
        if oniux curl --max-time 15 -s https://httpbin.org/ip; then
            log_info "✅ Alternative Tor-Verbindung erfolgreich"
            return 0
        else
            log_error "❌ Tor-Verbindung funktioniert nicht"
            return 1
        fi
    fi
}

# Fehlerbehebung-Tipps
show_troubleshooting() {
    cat << EOF

🔧 FEHLERBEHEBUNG:
==================

Falls Probleme auftreten:

1. Shell neu starten:
   exit
   # Neue SSH-Session starten

2. Manuell Umgebung neu laden:
   source ~/.bashrc
   source ~/.cargo/env

3. Rust komplett neu installieren:
   rustup self uninstall
   curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

4. System-Dependencies prüfen:
   sudo apt update
   sudo apt install build-essential pkg-config libssl-dev

5. Tor-Service prüfen:
   sudo systemctl status tor
   sudo systemctl start tor

EOF
}

# Main-Funktion
main() {
    log_info "🚀 Starte Rust & Oniux Setup..."
    
    if setup_rust_environment; then
        log_info "Schritt 1/4 ✅ Rust-Umgebung konfiguriert"
    else
        log_error "Schritt 1/4 ❌ Rust-Umgebung fehlgeschlagen"
        show_troubleshooting
        exit 1
    fi
    
    if install_oniux; then
        log_info "Schritt 2/4 ✅ Oniux installiert"
    else
        log_error "Schritt 2/4 ❌ Oniux-Installation fehlgeschlagen"
        show_troubleshooting
        exit 1
    fi
    
    if test_oniux; then
        log_info "Schritt 3/4 ✅ Oniux getestet"
    else
        log_error "Schritt 3/4 ❌ Oniux-Test fehlgeschlagen"
        show_troubleshooting
        exit 1
    fi
    
    if test_tor_connection; then
        log_info "Schritt 4/4 ✅ Tor-Verbindung erfolgreich"
    else
        log_warn "Schritt 4/4 ⚠️ Tor-Verbindung problematisch"
        log_info "Oniux ist installiert, aber Tor funktioniert möglicherweise nicht optimal"
    fi
    
    log_info "🎉 Setup abgeschlossen!"
    log_info "Sie können jetzt oniux verwenden:"
    echo "   oniux curl https://icanhazip.com"
    echo "   oniux bash"
}

# Script ausführen
main "$@"