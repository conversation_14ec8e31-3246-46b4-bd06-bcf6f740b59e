# 1. Oniux installieren
cargo install --git https://gitlab.torproject.org/tpo/core/oniux oniux@0.4.0

# 2. Erforderliche Python-Pakete (falls noch nicht vorhanden)
pip install requests  # Optional für erweiterte Features

# Basis-Scan eigener Infrastruktur
python3 oniux_security.py https://ihre-domain.com --authorized

# Mehrere Ziele scannen
python3 oniux_security.py https://server1.com https://server2.com ********** --authorized

# Mit spezifischer Output-Datei
python3 oniux_security.py https://ihre-app.com --authorized --output security_report.json

Wichtige Rechtliche und Ethische Hinweise:
⚠️ KRITISCH: Dieses Tool darf NUR verwendet werden für:

Ihre eigenen Systeme/Server
Systeme mit schriftlicher Autorisierung des Eigentümers
Autorisierte Penetrationstests in Ihrem Unternehmen

Features des Scripts:

Erreichbarkeitsprüfung: Testet ob Ziele über Tor erreichbar sind
Port-Scanning: Scannt häufige Ports auf Schwachstellen
Security Header Analyse: Bewertet HTTP-Sicherheitsheader
Umfassende Berichte: JSON-Export aller Ergebnisse
Logging: Vollständige Protokollierung aller Aktivitäten

Zusätzliche Sicherheitstipps:

Führen Sie Tests nur außerhalb der Geschäftszeiten durch
Informieren Sie Ihr IT-Team über geplante Tests
Dokumentieren Sie alle Autorisierungen
Verwenden Sie das Tool nur in isolierten Testumgebungen

Das Script respektiert ethische Hacking-Prinzipien und hilft Ihnen dabei, Ihre Infrastruktur sicher zu testen, während es gleichzeitig Missbrauch verhindert.