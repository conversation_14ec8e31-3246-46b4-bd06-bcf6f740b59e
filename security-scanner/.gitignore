# .gitignore für Security/Penetrationstesting Projekte
# =====================================================

# KRITISCH: Sensitive Daten niemals committen!
# =====================================================

# Credentials und Secrets
*.key
*.pem  
*.p12
*.pfx
*.crt
*.cert
*.csr
*password*
*secret*
*token*
*api_key*
*credential*
config.json
secrets.json
.env
.env.*
auth.json
private_keys/
certificates/

# Scan Ergebnisse und Reports
# =====================================================
*_results.json
*_report.html
*_report.pdf
*_scan_*
scan_results/
reports/
evidence/
screenshots/
poc/
exploits/
loot/
dumps/

# Target Listen und Sensitive Info
# =====================================================
targets.txt
target_list.*
hosts.txt
ips.txt
domains.txt
*_targets.*
scope.txt
client_data/
infrastructure_info/

# Log Files
# =====================================================
*.log
*.log.*
logs/
debug.log
error.log
access.log
nmap.log
burp.log
metasploit.log
audit.log

# Python
# =====================================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
# =====================================================
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
pyvenv.cfg

# IDE und Editor Files
# =====================================================
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Penetrationstesting Tools Output
# =====================================================

# Nmap
*.nmap
*.gnmap
*.xml

# Burp Suite
burp_state/
burp_temp/
*.burp

# Metasploit
.msf4/
loot/
logs/

# SQLMap
sqlmap_output/
*.csv

# Nikto
nikto_results/

# Dirb/Dirbuster
dirb_results/
wordlists/custom/

# Wireshark/tcpdump
*.pcap
*.pcapng
*.cap

# Hydra
hydra.log
hydra.restore

# John the Ripper
john.pot
*.pot

# Hashcat
hashcat.potfile
*.hcmask
*.hcchr

# Custom Tools Output
# =====================================================
nuclei_results/
subfinder_results/
amass_output/
recon_results/
enum_results/

# Database Files
# =====================================================
*.db
*.sqlite
*.sqlite3
*.db3

# Compressed Files (potential malware/tools)
# =====================================================
*.zip
*.rar
*.7z
*.tar.gz
*.tgz
*.bz2
*.exe
*.msi
*.dmg
*.pkg

# Temporary Files
# =====================================================
tmp/
temp/
cache/
.cache/
*.tmp
*.temp
*.bak
*.backup
*.old

# OS Generated Files
# =====================================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Docker
# =====================================================
docker-compose.override.yml
.dockerignore

# Terraform (if using for infrastructure)
# =====================================================
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Kubernetes
# =====================================================
*.kubeconfig
kubeconfig

# Web Shells und Malware Samples
# =====================================================
webshells/
malware_samples/
*.jsp
*.aspx
*.asp
*shell*
*backdoor*

# Network Captures
# =====================================================
network_captures/
traffic_analysis/

# Memory Dumps
# =====================================================
*.dmp
*.vmem
*.raw
memory_dumps/

# Forensic Evidence
# =====================================================
evidence/
forensics/
timeline/
artifacts/

# Client Specific (Template - anpassen!)
# =====================================================
# client_name/
# project_xyz/
# engagement_*/

# Documentation mit sensitiven Infos
# =====================================================
# Nur Templates committen, keine ausgefüllten Docs!
*_filled.docx
*_completed.pdf
*_final_report.*

# Backup Files von Editoren
# =====================================================
*~
.#*
\#*#
*.bak

# Node.js (falls verwendet)
# =====================================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python Jupyter Notebooks mit Output
# =====================================================
.ipynb_checkpoints/
*-checkpoint.ipynb

# Custom Section - Projektspezifisch
# =====================================================
# Hier eigene Ergänzungen hinzufügen:

# Beispiel für oniux spezifische Files:
oniux_logs/
tor_data/
.tor/

# WICHTIGER HINWEIS:
# ==================
# Immer vor dem ersten Commit prüfen:
# git status
# git diff --cached
# 
# Bei versehentlichem Commit sensitiver Daten:
# git filter-branch oder BFG Repo-Cleaner verwenden!
#
# Für bereits commitete sensitive Daten:
# 1. Repository History komplett bereinigen
# 2. Alle lokalen Kopien löschen
# 3. Neue Credentials generieren
# 4. Clients informieren